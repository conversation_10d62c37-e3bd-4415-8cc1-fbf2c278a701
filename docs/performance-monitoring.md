# Sistema de Monitoreo de Rendimiento - Salonier

**Fecha**: 2025-01-15  
**Versión**: v2.0.5  

## 🎯 Objetivos del Monitoreo

### Métricas Clave a Monitorear
1. **Tiempo de Carga Inicial**: < 3 segundos
2. **Re-renders por Componente**: < 5 por acción
3. **Uso de Memoria**: < 200MB en dispositivos promedio
4. **Cache Hit Ratio**: > 85% en servicios críticos
5. **Queries DB**: < 500ms promedio
6. **Bundle Size**: < 50MB total

## 📊 Herramientas de Monitoreo

### 1. React Native Performance Monitor

```typescript
// utils/performance-monitor.ts
class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();
  
  static startMeasure(name: string): void {
    if (__DEV__) {
      performance.mark(`${name}-start`);
    }
  }
  
  static endMeasure(name: string): number {
    if (__DEV__) {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      
      const measure = performance.getEntriesByName(name)[0];
      const duration = measure.duration;
      
      // Almacenar métrica
      if (!this.metrics.has(name)) {
        this.metrics.set(name, []);
      }
      this.metrics.get(name)!.push(duration);
      
      // Log si excede threshold
      if (duration > 100) {
        console.warn(`⚠️ Slow operation: ${name} took ${duration.toFixed(2)}ms`);
      }
      
      return duration;
    }
    return 0;
  }
  
  static getMetrics(): Record<string, { avg: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((values, name) => {
      result[name] = {
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        max: Math.max(...values),
        count: values.length
      };
    });
    
    return result;
  }
  
  static clearMetrics(): void {
    this.metrics.clear();
  }
}

export { PerformanceMonitor };
```

### 2. Component Render Tracker

```typescript
// utils/render-tracker.ts
import React from 'react';

const renderCounts = new Map<string, number>();

export const withRenderTracking = <P extends object>(
  Component: React.ComponentType<P>,
  name: string
) => {
  return React.memo((props: P) => {
    if (__DEV__) {
      const count = renderCounts.get(name) || 0;
      renderCounts.set(name, count + 1);
      
      if (count > 10) {
        console.warn(`🔄 High render count for ${name}: ${count}`);
      }
    }
    
    return <Component {...props} />;
  });
};

export const getRenderCounts = () => {
  return Object.fromEntries(renderCounts);
};

export const clearRenderCounts = () => {
  renderCounts.clear();
};
```

### 3. Memory Usage Monitor

```typescript
// utils/memory-monitor.ts
class MemoryMonitor {
  private static interval: NodeJS.Timeout | null = null;
  private static samples: number[] = [];
  
  static startMonitoring(): void {
    if (__DEV__ && !this.interval) {
      this.interval = setInterval(() => {
        if (performance.memory) {
          const used = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
          this.samples.push(used);
          
          // Mantener solo las últimas 100 muestras
          if (this.samples.length > 100) {
            this.samples.shift();
          }
          
          // Alertar si el uso excede 200MB
          if (used > 200) {
            console.warn(`🧠 High memory usage: ${used.toFixed(2)}MB`);
          }
        }
      }, 5000); // Cada 5 segundos
    }
  }
  
  static stopMonitoring(): void {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }
  
  static getMemoryStats(): { current: number; avg: number; max: number } {
    if (this.samples.length === 0) return { current: 0, avg: 0, max: 0 };
    
    const current = this.samples[this.samples.length - 1];
    const avg = this.samples.reduce((a, b) => a + b, 0) / this.samples.length;
    const max = Math.max(...this.samples);
    
    return { current, avg, max };
  }
}

export { MemoryMonitor };
```

### 4. Database Query Monitor

```typescript
// utils/db-monitor.ts
class DatabaseMonitor {
  private static queries: Array<{
    query: string;
    duration: number;
    timestamp: number;
  }> = [];
  
  static logQuery(query: string, duration: number): void {
    this.queries.push({
      query,
      duration,
      timestamp: Date.now()
    });
    
    // Mantener solo las últimas 50 queries
    if (this.queries.length > 50) {
      this.queries.shift();
    }
    
    // Alertar queries lentas
    if (duration > 1000) {
      console.warn(`🐌 Slow query (${duration}ms):`, query.substring(0, 100));
    }
  }
  
  static getSlowQueries(threshold = 500): typeof this.queries {
    return this.queries.filter(q => q.duration > threshold);
  }
  
  static getAverageQueryTime(): number {
    if (this.queries.length === 0) return 0;
    return this.queries.reduce((sum, q) => sum + q.duration, 0) / this.queries.length;
  }
}

export { DatabaseMonitor };
```

## 🔧 Implementación en Stores

### Store con Monitoreo

```typescript
// stores/monitored-store.ts
import { create } from 'zustand';
import { PerformanceMonitor } from '../utils/performance-monitor';
import { DatabaseMonitor } from '../utils/db-monitor';

export const useMonitoredStore = create<StoreType>((set, get) => ({
  data: [],
  loading: false,
  
  fetchData: async () => {
    PerformanceMonitor.startMeasure('fetchData');
    
    set({ loading: true });
    
    try {
      const startTime = Date.now();
      const data = await api.fetchData();
      const duration = Date.now() - startTime;
      
      DatabaseMonitor.logQuery('fetchData', duration);
      
      set({ data, loading: false });
    } catch (error) {
      set({ loading: false });
      throw error;
    } finally {
      PerformanceMonitor.endMeasure('fetchData');
    }
  }
}));
```

## 📈 Dashboard de Métricas

### Componente de Métricas (Solo Dev)

```typescript
// components/dev/MetricsDashboard.tsx
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { PerformanceMonitor } from '../../utils/performance-monitor';
import { MemoryMonitor } from '../../utils/memory-monitor';
import { getRenderCounts } from '../../utils/render-tracker';

const MetricsDashboard = () => {
  const [metrics, setMetrics] = useState<any>({});
  const [memoryStats, setMemoryStats] = useState<any>({});
  const [renderCounts, setRenderCounts] = useState<any>({});
  
  useEffect(() => {
    if (!__DEV__) return;
    
    const interval = setInterval(() => {
      setMetrics(PerformanceMonitor.getMetrics());
      setMemoryStats(MemoryMonitor.getMemoryStats());
      setRenderCounts(getRenderCounts());
    }, 2000);
    
    return () => clearInterval(interval);
  }, []);
  
  if (!__DEV__) return null;
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>📊 Performance Metrics</Text>
      
      {/* Memory Stats */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🧠 Memory Usage</Text>
        <Text>Current: {memoryStats.current?.toFixed(2)}MB</Text>
        <Text>Average: {memoryStats.avg?.toFixed(2)}MB</Text>
        <Text>Peak: {memoryStats.max?.toFixed(2)}MB</Text>
      </View>
      
      {/* Performance Metrics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>⚡ Performance</Text>
        {Object.entries(metrics).map(([name, stats]: [string, any]) => (
          <View key={name} style={styles.metric}>
            <Text style={styles.metricName}>{name}</Text>
            <Text>Avg: {stats.avg?.toFixed(2)}ms</Text>
            <Text>Max: {stats.max?.toFixed(2)}ms</Text>
            <Text>Count: {stats.count}</Text>
          </View>
        ))}
      </View>
      
      {/* Render Counts */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔄 Render Counts</Text>
        {Object.entries(renderCounts).map(([name, count]: [string, any]) => (
          <Text key={name} style={count > 10 ? styles.highCount : styles.normalCount}>
            {name}: {count}
          </Text>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100,
    right: 10,
    width: 250,
    backgroundColor: 'rgba(0,0,0,0.8)',
    padding: 10,
    borderRadius: 5,
    maxHeight: 400,
  },
  title: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    color: 'yellow',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  metric: {
    marginBottom: 5,
    paddingLeft: 10,
  },
  metricName: {
    color: 'cyan',
    fontWeight: 'bold',
  },
  normalCount: {
    color: 'white',
  },
  highCount: {
    color: 'red',
    fontWeight: 'bold',
  },
});

export default MetricsDashboard;
```

## 🚨 Alertas y Thresholds

### Sistema de Alertas

```typescript
// utils/alert-system.ts
interface Alert {
  type: 'performance' | 'memory' | 'render' | 'database';
  severity: 'low' | 'medium' | 'high';
  message: string;
  timestamp: number;
  data?: any;
}

class AlertSystem {
  private static alerts: Alert[] = [];
  private static listeners: Array<(alert: Alert) => void> = [];
  
  static addAlert(alert: Omit<Alert, 'timestamp'>): void {
    const fullAlert: Alert = {
      ...alert,
      timestamp: Date.now()
    };
    
    this.alerts.push(fullAlert);
    
    // Mantener solo las últimas 20 alertas
    if (this.alerts.length > 20) {
      this.alerts.shift();
    }
    
    // Notificar listeners
    this.listeners.forEach(listener => listener(fullAlert));
    
    // Log en consola
    const emoji = alert.severity === 'high' ? '🚨' : 
                  alert.severity === 'medium' ? '⚠️' : 'ℹ️';
    console.warn(`${emoji} [${alert.type.toUpperCase()}] ${alert.message}`);
  }
  
  static subscribe(listener: (alert: Alert) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
  
  static getAlerts(): Alert[] {
    return [...this.alerts];
  }
  
  static clearAlerts(): void {
    this.alerts = [];
  }
}

export { AlertSystem, type Alert };
```

## 📋 Checklist de Monitoreo

### Setup Inicial
- [ ] Implementar PerformanceMonitor en operaciones críticas
- [ ] Configurar MemoryMonitor en app startup
- [ ] Agregar RenderTracker a componentes principales
- [ ] Configurar DatabaseMonitor en stores
- [ ] Implementar AlertSystem

### Métricas Diarias
- [ ] Revisar alertas de rendimiento
- [ ] Verificar uso de memoria promedio
- [ ] Analizar queries lentas
- [ ] Revisar componentes con muchos re-renders
- [ ] Verificar cache hit ratios

### Optimizaciones Semanales
- [ ] Analizar tendencias de rendimiento
- [ ] Identificar nuevos bottlenecks
- [ ] Optimizar componentes problemáticos
- [ ] Ajustar thresholds de alertas
- [ ] Documentar mejoras implementadas

---

*Sistema de monitoreo activo - Actualizar métricas regularmente*
