# Reporte de Optimización de Base de Datos - Salonier

**Fecha**: 2025-01-15  
**Proyecto**: ajsamgugqfbttkrlgvbr (Salonier)  
**Región**: eu-west-3  
**PostgreSQL**: **********  

## 📊 Análisis Actual

### Tablas Principales
- `ai_analysis_cache` - Cache de análisis IA
- `cache_metrics` - Métricas de cache
- `client_consents` - Consentimientos de clientes
- `clients` - Clientes del salón
- `formulas` - Fórmulas generadas
- `products` - Inventario de productos
- `profiles` - Perfiles de usuarios
- `salons` - Información de salones
- `services` - Servicios realizados
- `stock_movements` - Movimientos de inventario
- `v_salon_products_summary` - Vista resumen de productos

## 🔍 Índices Existentes

### ✅ Bien Optimizados
- **ai_analysis_cache**: Índices compuestos eficientes para lookup y expiración
- **products**: <PERSON>úl<PERSON><PERSON> índices especializados (brand_line, category, color_code, stock_alerts)
- **clients**: Índices en salon_id y created_by

### ⚠️ Oportunidades de Mejora
- **services**: Solo tiene primary key, falta índices para queries frecuentes
- **formulas**: Solo tiene primary key e índice en created_by
- **stock_movements**: Podría beneficiarse de índices compuestos

## 🛡️ Políticas RLS

### ✅ Correctamente Implementadas
- Todas las tablas principales tienen políticas RLS
- Patrón consistente: acceso por salon_id via profiles
- Separación clara entre service_role y public

### 🔧 Optimizaciones Sugeridas
- Las políticas RLS realizan subconsulta a profiles en cada operación
- Considerar materializar salon_id en contexto de sesión

## 📈 Recomendaciones de Optimización

### 1. Índices Faltantes (Alta Prioridad)

```sql
-- Servicios por salón y fecha
CREATE INDEX idx_services_salon_date ON services(salon_id, created_at DESC);

-- Servicios por cliente
CREATE INDEX idx_services_client_id ON services(client_id);

-- Fórmulas por salón
CREATE INDEX idx_formulas_salon_id ON formulas(salon_id);

-- Movimientos de stock por producto y fecha
CREATE INDEX idx_stock_movements_product_date ON stock_movements(product_id, created_at DESC);

-- Movimientos de stock compuesto
CREATE INDEX idx_stock_movements_salon_product ON stock_movements(salon_id, product_id);
```

### 2. Optimización de RLS (Media Prioridad)

```sql
-- Función para obtener salon_id del usuario actual (cacheable)
CREATE OR REPLACE FUNCTION get_user_salon_id()
RETURNS UUID
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT salon_id FROM profiles WHERE id = auth.uid();
$$;

-- Ejemplo de política optimizada
DROP POLICY IF EXISTS "Gestión de servicios por miembros del salón." ON services;
CREATE POLICY "Gestión de servicios por miembros del salón."
ON services FOR ALL TO public
USING (salon_id = get_user_salon_id());
```

### 3. Particionamiento (Baja Prioridad)

Para tablas que crecen mucho:
```sql
-- Particionar ai_analysis_cache por fecha
-- Particionar stock_movements por mes
-- Particionar services por trimestre
```

### 4. Vistas Materializadas

```sql
-- Vista materializada para estadísticas de productos
CREATE MATERIALIZED VIEW mv_product_stats AS
SELECT 
  salon_id,
  category,
  brand,
  COUNT(*) as total_products,
  SUM(stock_ml) as total_stock_ml,
  COUNT(*) FILTER (WHERE stock_ml <= minimum_stock_ml) as low_stock_count
FROM products 
WHERE is_active = true
GROUP BY salon_id, category, brand;

-- Refrescar cada hora
CREATE INDEX ON mv_product_stats(salon_id, category);
```

### 5. Limpieza Automática

```sql
-- Función para limpiar cache expirado
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS void
LANGUAGE SQL
AS $$
  DELETE FROM ai_analysis_cache WHERE expires_at < NOW();
$$;

-- Programar limpieza diaria con pg_cron
SELECT cron.schedule('cleanup-cache', '0 2 * * *', 'SELECT cleanup_expired_cache();');
```

## 🚀 Plan de Implementación

### Fase 1: Índices Críticos (Inmediato)
- [ ] Crear índices en services
- [ ] Crear índices en formulas
- [ ] Crear índices compuestos en stock_movements

### Fase 2: Optimización RLS (1-2 semanas)
- [ ] Implementar función get_user_salon_id()
- [ ] Actualizar políticas RLS para usar la función
- [ ] Medir mejora de rendimiento

### Fase 3: Vistas Materializadas (1 mes)
- [ ] Crear vista materializada para estadísticas
- [ ] Implementar refresh automático
- [ ] Actualizar queries del frontend

### Fase 4: Mantenimiento Automático (2 meses)
- [ ] Configurar pg_cron para limpieza
- [ ] Implementar particionamiento si es necesario
- [ ] Monitoreo de rendimiento

## 📊 Métricas a Monitorear

### Queries Lentas
```sql
-- Habilitar log de queries lentas
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 1 segundo
SELECT pg_reload_conf();
```

### Estadísticas de Índices
```sql
-- Verificar uso de índices
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

### Tamaño de Tablas
```sql
-- Monitorear crecimiento de tablas
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 🎯 Resultados Esperados

- **Queries de servicios**: 50-70% más rápidas
- **Carga de inventario**: 30-40% más rápida  
- **Políticas RLS**: 20-30% menos overhead
- **Cache hit ratio**: >95% en ai_analysis_cache
- **Limpieza automática**: Reducir crecimiento de BD

## ⚠️ Consideraciones

- Implementar cambios en horarios de bajo tráfico
- Hacer backup antes de cambios estructurales
- Monitorear rendimiento post-implementación
- Rollback plan para cada cambio

---

*Generado por Claude Code - 2025-01-15*
