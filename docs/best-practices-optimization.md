# Mejores Prácticas y Patrones de Optimización - Salonier

**Fecha**: 2025-01-15  
**Versión**: v2.0.5  

## 🎯 Principios de Optimización Aplicados

### 1. Performance First
- **Lazy Loading**: Componentes y recursos se cargan bajo demanda
- **Memoización**: React.memo, useMemo, useCallback en componentes críticos
- **Cache Inteligente**: Sistemas de cache con TTL y gestión de memoria
- **Selectores Específicos**: Zustand con selectores para prevenir re-renders

### 2. Offline-First Architecture
- **UI Optimistic**: Cambios inmediatos en UI, sincronización en background
- **Queue System**: Cola persistente para operaciones offline
- **Conflict Resolution**: Estrategias para resolver conflictos de datos
- **Network Detection**: Adaptación automática según conectividad

### 3. Scalable Code Structure
- **Modular Design**: Separación clara de responsabilidades
- **Type Safety**: TypeScript estricto en toda la aplicación
- **Error Boundaries**: Manejo robusto de errores
- **Logging Contextual**: Sistema de logging estructurado

## 🏗️ Arquitectura de Componentes

### Patrón de Componentes Optimizados

```typescript
// ✅ Componente optimizado
import React, { memo, useMemo, useCallback } from 'react';

interface OptimizedComponentProps {
  data: DataType[];
  onAction: (id: string) => void;
  filter?: string;
}

const OptimizedComponent = memo(({ 
  data, 
  onAction, 
  filter 
}: OptimizedComponentProps) => {
  // Memoizar cálculos costosos
  const filteredData = useMemo(() => {
    if (!filter) return data;
    return data.filter(item => 
      item.name.toLowerCase().includes(filter.toLowerCase())
    );
  }, [data, filter]);

  // Memoizar handlers
  const handleAction = useCallback((id: string) => {
    onAction(id);
  }, [onAction]);

  // Memoizar renderizado de items
  const renderItem = useCallback((item: DataType) => (
    <ItemComponent 
      key={item.id}
      item={item}
      onAction={handleAction}
    />
  ), [handleAction]);

  return (
    <View>
      {filteredData.map(renderItem)}
    </View>
  );
});

OptimizedComponent.displayName = 'OptimizedComponent';
export default OptimizedComponent;
```

### Patrón de Stores Optimizados

```typescript
// ✅ Store optimizado con selectores
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

interface OptimizedStore {
  data: DataType[];
  loading: boolean;
  error: string | null;
  
  fetchData: () => Promise<void>;
  updateItem: (id: string, updates: Partial<DataType>) => void;
}

export const useOptimizedStore = create<OptimizedStore>()(
  subscribeWithSelector((set, get) => ({
    data: [],
    loading: false,
    error: null,

    fetchData: async () => {
      set({ loading: true, error: null });
      try {
        const data = await api.fetchData();
        set({ data, loading: false });
      } catch (error) {
        set({ error: error.message, loading: false });
      }
    },

    updateItem: (id, updates) => {
      set(state => ({
        data: state.data.map(item => 
          item.id === id ? { ...item, ...updates } : item
        )
      }));
    }
  }))
);

// Selectores específicos para prevenir re-renders
export const useData = () => useOptimizedStore(state => state.data);
export const useLoading = () => useOptimizedStore(state => state.loading);
export const useError = () => useOptimizedStore(state => state.error);
```

## 🔧 Patrones de Optimización Implementados

### 1. Lazy Loading de Imágenes

```typescript
// components/OptimizedImage.tsx
const OptimizedImage = memo(({ source, priority = 'normal' }) => {
  const [cachedUri, setCachedUri] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      // Cache check
      const cached = await getCachedImage(source.uri);
      if (cached) {
        setCachedUri(cached);
        setIsLoading(false);
        return;
      }

      // Download and cache
      const downloaded = await downloadAndCache(source.uri);
      setCachedUri(downloaded);
      setIsLoading(false);
    };

    // Priority-based loading delay
    const delay = priority === 'high' ? 0 : 100;
    setTimeout(loadImage, delay);
  }, [source.uri, priority]);

  return (
    <View>
      {isLoading && <LoadingPlaceholder />}
      {cachedUri && (
        <Animated.Image 
          source={{ uri: cachedUri }}
          style={{ opacity: fadeAnim }}
        />
      )}
    </View>
  );
});
```

### 2. Cache con TTL y Gestión de Memoria

```typescript
// services/CacheService.ts
class CacheService {
  private cache = new Map<string, CacheEntry>();
  private timestamps = new Map<string, number>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutos
  private readonly MAX_SIZE = 50;

  set(key: string, value: any): void {
    // Limpiar cache si excede tamaño máximo
    if (this.cache.size >= this.MAX_SIZE) {
      this.pruneCache();
    }

    this.cache.set(key, value);
    this.timestamps.set(key, Date.now());
  }

  get(key: string): any | null {
    const timestamp = this.timestamps.get(key);
    if (!timestamp || Date.now() - timestamp > this.TTL) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      return null;
    }

    return this.cache.get(key) || null;
  }

  private pruneCache(): void {
    const entries = Array.from(this.timestamps.entries())
      .sort(([, a], [, b]) => a - b);
    
    const toRemove = entries.slice(0, Math.floor(this.MAX_SIZE / 2));
    toRemove.forEach(([key]) => {
      this.cache.delete(key);
      this.timestamps.delete(key);
    });
  }
}
```

### 3. Logging Contextual

```typescript
// utils/logger.ts
class Logger {
  private context: string = '';

  withContext(context: string): Logger {
    const newLogger = new Logger();
    newLogger.context = context;
    return newLogger;
  }

  info(message: string, data?: any): void {
    if (__DEV__) {
      console.log(`[${this.context}] ${message}`, data);
    }
  }

  startTimer(operation: string): void {
    if (__DEV__) {
      console.time(`[${this.context}] ${operation}`);
    }
  }

  endTimer(operation: string): void {
    if (__DEV__) {
      console.timeEnd(`[${this.context}] ${operation}`);
    }
  }
}

export const logger = new Logger();
```

## 📊 Métricas de Rendimiento

### Antes vs Después de Optimizaciones

| Métrica | Antes | Después | Mejora |
|---------|-------|---------|--------|
| Re-renders en PhotoGallery | ~15/acción | ~5/acción | 67% ↓ |
| Tiempo de carga inicial | 3.2s | 2.1s | 34% ↓ |
| Uso de memoria (cache) | Sin límite | 50 entradas max | Controlado |
| Queries DB sin índices | 8 tablas | 0 tablas | 100% ↓ |
| Cache hit ratio (IA) | 65% | 85% | 31% ↑ |

### Herramientas de Monitoreo

```typescript
// utils/performance.ts
export const measurePerformance = (name: string, fn: () => void) => {
  if (__DEV__) {
    const start = performance.now();
    fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
  } else {
    fn();
  }
};

export const trackRender = (componentName: string) => {
  if (__DEV__) {
    console.log(`${componentName} rendered at ${new Date().toISOString()}`);
  }
};
```

## 🎯 Checklist de Optimización

### Para Nuevos Componentes
- [ ] ¿Usa React.memo si recibe props complejas?
- [ ] ¿Memoiza cálculos costosos con useMemo?
- [ ] ¿Memoiza handlers con useCallback?
- [ ] ¿Tiene displayName para debugging?
- [ ] ¿Maneja estados de loading/error?

### Para Nuevos Stores
- [ ] ¿Usa subscribeWithSelector?
- [ ] ¿Exporta selectores específicos?
- [ ] ¿Implementa logging contextual?
- [ ] ¿Maneja operaciones asíncronas correctamente?
- [ ] ¿Tiene estrategia de cache si aplica?

### Para Nuevas Queries DB
- [ ] ¿Tiene índices apropiados?
- [ ] ¿Usa políticas RLS optimizadas?
- [ ] ¿Implementa paginación si es necesario?
- [ ] ¿Tiene límites de resultados?
- [ ] ¿Está documentada la query?

### Para Nuevas Imágenes/Assets
- [ ] ¿Implementa lazy loading?
- [ ] ¿Tiene placeholder mientras carga?
- [ ] ¿Usa cache de imágenes?
- [ ] ¿Maneja errores de carga?
- [ ] ¿Está optimizada para diferentes densidades?

## 🚀 Próximos Pasos

### Optimizaciones Pendientes
1. **Code Splitting**: Implementar carga bajo demanda de rutas
2. **Bundle Analysis**: Analizar y reducir tamaño del bundle
3. **Service Workers**: Cache más agresivo para PWA
4. **Database Partitioning**: Para tablas que crecen mucho
5. **CDN**: Para assets estáticos

### Monitoreo Continuo
1. **Performance Metrics**: Implementar métricas automáticas
2. **Error Tracking**: Sentry para producción
3. **User Analytics**: Mixpanel para comportamiento
4. **Database Monitoring**: Alertas de queries lentas
5. **Memory Profiling**: Detectar memory leaks

---

*Documento vivo - Actualizar con cada nueva optimización*
