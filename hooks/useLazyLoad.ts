/**
 * useLazyLoad Hook
 * 
 * Hook personalizado para implementar lazy loading de componentes
 * y recursos de forma eficiente
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { Dimensions } from 'react-native';
import { logger } from '@/utils/logger';

interface LazyLoadOptions {
  threshold?: number; // Distancia en píxeles para activar la carga
  rootMargin?: number; // Margen adicional para la detección
  delay?: number; // Retraso antes de cargar
  priority?: 'low' | 'normal' | 'high';
}

interface LazyLoadResult {
  isVisible: boolean;
  shouldLoad: boolean;
  ref: React.RefObject<any>;
  load: () => void;
  reset: () => void;
}

/**
 * Hook para lazy loading basado en visibilidad
 */
export const useLazyLoad = (options: LazyLoadOptions = {}): LazyLoadResult => {
  const {
    threshold = 100,
    rootMargin = 50,
    delay = 0,
    priority = 'normal'
  } = options;

  const [isVisible, setIsVisible] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(false);
  const ref = useRef<any>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const observerRef = useRef<any>(null);

  // Simular Intersection Observer para React Native
  const checkVisibility = useCallback(() => {
    if (!ref.current) return;

    ref.current.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
      const screenHeight = Dimensions.get('window').height;
      const screenWidth = Dimensions.get('window').width;

      const isInViewport = (
        pageY + height + rootMargin >= 0 &&
        pageY - rootMargin <= screenHeight &&
        pageX + width + rootMargin >= 0 &&
        pageX - rootMargin <= screenWidth
      );

      if (isInViewport && !isVisible) {
        setIsVisible(true);
        
        // Aplicar retraso según prioridad
        const loadDelay = priority === 'high' ? 0 : 
                         priority === 'normal' ? delay : 
                         delay + 200;

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
          setShouldLoad(true);
          logger.debug('Lazy load triggered for component');
        }, loadDelay);
      }
    });
  }, [isVisible, rootMargin, delay, priority]);

  const load = useCallback(() => {
    setShouldLoad(true);
  }, []);

  const reset = useCallback(() => {
    setIsVisible(false);
    setShouldLoad(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  useEffect(() => {
    // Configurar observador de scroll (simplificado para React Native)
    const interval = setInterval(checkVisibility, 100);

    return () => {
      clearInterval(interval);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [checkVisibility]);

  return {
    isVisible,
    shouldLoad,
    ref,
    load,
    reset
  };
};

/**
 * Hook para lazy loading de listas grandes
 */
export const useLazyList = <T>(
  items: T[],
  initialCount: number = 10,
  incrementCount: number = 5
) => {
  const [visibleCount, setVisibleCount] = useState(initialCount);
  const [isLoading, setIsLoading] = useState(false);

  const loadMore = useCallback(() => {
    if (isLoading || visibleCount >= items.length) return;

    setIsLoading(true);
    
    // Simular carga asíncrona
    setTimeout(() => {
      setVisibleCount(prev => Math.min(prev + incrementCount, items.length));
      setIsLoading(false);
    }, 100);
  }, [isLoading, visibleCount, items.length, incrementCount]);

  const reset = useCallback(() => {
    setVisibleCount(initialCount);
    setIsLoading(false);
  }, [initialCount]);

  const visibleItems = items.slice(0, visibleCount);
  const hasMore = visibleCount < items.length;

  return {
    visibleItems,
    hasMore,
    isLoading,
    loadMore,
    reset,
    visibleCount,
    totalCount: items.length
  };
};

/**
 * Hook para lazy loading de recursos (imágenes, datos, etc.)
 */
export const useLazyResource = <T>(
  loader: () => Promise<T>,
  dependencies: any[] = [],
  options: { immediate?: boolean; cache?: boolean } = {}
) => {
  const { immediate = false, cache = true } = options;
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const cacheRef = useRef<Map<string, T>>(new Map());
  const loadedRef = useRef(false);

  const cacheKey = JSON.stringify(dependencies);

  const load = useCallback(async () => {
    if (loading || (loadedRef.current && !error)) return;

    // Verificar cache
    if (cache && cacheRef.current.has(cacheKey)) {
      const cachedData = cacheRef.current.get(cacheKey);
      setData(cachedData!);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await loader();
      setData(result);
      loadedRef.current = true;

      // Guardar en cache
      if (cache) {
        cacheRef.current.set(cacheKey, result);
        
        // Limpiar cache si tiene demasiadas entradas
        if (cacheRef.current.size > 10) {
          const firstKey = cacheRef.current.keys().next().value;
          cacheRef.current.delete(firstKey);
        }
      }
    } catch (err) {
      setError(err as Error);
      logger.error('Lazy resource loading failed', err);
    } finally {
      setLoading(false);
    }
  }, [loader, loading, error, cache, cacheKey]);

  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    setError(null);
    loadedRef.current = false;
  }, []);

  useEffect(() => {
    if (immediate) {
      load();
    }
  }, [immediate, load]);

  return {
    data,
    loading,
    error,
    load,
    reset,
    loaded: loadedRef.current
  };
};

/**
 * Hook para precargar recursos en background
 */
export const usePreload = <T>(
  loaders: (() => Promise<T>)[],
  priority: 'low' | 'normal' | 'high' = 'low'
) => {
  const [preloadedCount, setPreloadedCount] = useState(0);
  const [isPreloading, setIsPreloading] = useState(false);
  const resultsRef = useRef<T[]>([]);

  const startPreload = useCallback(async () => {
    if (isPreloading) return;

    setIsPreloading(true);
    setPreloadedCount(0);
    resultsRef.current = [];

    const delay = priority === 'high' ? 0 : 
                  priority === 'normal' ? 100 : 200;

    for (let i = 0; i < loaders.length; i++) {
      try {
        // Retraso entre cargas para no bloquear la UI
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const result = await loaders[i]();
        resultsRef.current[i] = result;
        setPreloadedCount(i + 1);
      } catch (error) {
        logger.error(`Preload failed for item ${i}`, error);
      }
    }

    setIsPreloading(false);
  }, [loaders, priority, isPreloading]);

  return {
    preloadedCount,
    totalCount: loaders.length,
    isPreloading,
    progress: loaders.length > 0 ? preloadedCount / loaders.length : 0,
    results: resultsRef.current,
    startPreload
  };
};
