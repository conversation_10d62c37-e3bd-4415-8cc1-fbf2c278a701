const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configuración para mayor estabilidad
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Sin timeout para evitar desconexiones
      req.setTimeout(0);
      return middleware(req, res, next);
    };
  },
};

// Resetear caché más agresivamente si hay problemas
config.resetCache = true;

module.exports = config;