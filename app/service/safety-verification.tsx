import React, { useState, useRef, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Alert, Switch, Animated, TextInput } from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { ChevronLeft, Shield, CheckCircle, AlertTriangle, FileText, Clock, Users, Info } from "lucide-react-native";
import Colors from "@/constants/colors";
import { useClientStore } from "@/stores/client-store";
import { useClientHistoryStore } from "@/stores/client-history-store";
import SignatureCanvas from "@/components/SignatureCanvas";
import { COMMON_ALLERGIES } from "@/constants/common-allergies";

interface SafetyCheckItem {
  id: string;
  title: string;
  description: string;
  required: boolean;
  checked: boolean;
}

interface ConsentItem {
  id: string;
  title: string;
  description: string;
  accepted: boolean;
}

export default function SafetyVerificationScreen() {
  const { clientId } = useLocalSearchParams();
  const [client, setClient] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(0); // 0: Safety Check, 1: Patch Test, 2: Critical Checks, 3: Consent
  const [detectedRisks, setDetectedRisks] = useState<string[]>([]);
  
  const { getClient } = useClientStore();
  const { 
    getClientProfile, 
    addPatchTest, 
    addConsentRecord,
    getLastPatchTest,
    initializeClientProfile 
  } = useClientHistoryStore();

  // Safety checklist state
  const [safetyChecks, setSafetyChecks] = useState<SafetyCheckItem[]>([
    {
      id: "gloves",
      title: "Guantes desechables",
      description: "Guantes de nitrilo o vinilo disponibles y en uso",
      required: true,
      checked: false
    },
    {
      id: "ventilation",
      title: "Ventilación adecuada",
      description: "Área bien ventilada o sistema de extracción funcionando",
      required: true,
      checked: false
    },
    {
      id: "materials",
      title: "Materiales desechables",
      description: "Toallas, capas y materiales de un solo uso preparados",
      required: true,
      checked: false
    },
    {
      id: "products",
      title: "Productos en buen estado",
      description: "Verificación de fechas de caducidad y estado de productos",
      required: true,
      checked: false
    },
    {
      id: "workspace",
      title: "Área de trabajo limpia",
      description: "Superficie desinfectada y organizada",
      required: true,
      checked: false
    },
    {
      id: "emergency",
      title: "Kit de emergencia",
      description: "Acceso a agua corriente y kit de primeros auxilios",
      required: true,
      checked: false
    }
  ]);

  // Patch test state
  const [patchTestCompleted, setPatchTestCompleted] = useState(false);
  const [patchTestResult, setPatchTestResult] = useState<'negativo' | 'positivo' | 'pendiente'>('pendiente');
  const [patchTestNotes, setPatchTestNotes] = useState("");
  
  // Critical chemical compatibility checks
  const [metalTestResult, setMetalTestResult] = useState<'negative' | 'positive' | 'not_tested'>('not_tested');
  const [hennaPresence, setHennaPresence] = useState<boolean>(false);
  const [formaldehydeHistory, setFormaldehydeHistory] = useState<boolean>(false);
  const [homeRemediesUsed, setHomeRemediesUsed] = useState<boolean>(false);
  const [criticalNotes, setCriticalNotes] = useState("");

  // Consent state
  const [consentItems, setConsentItems] = useState<ConsentItem[]>([
    {
      id: "chemical_risks",
      title: "Riesgos químicos",
      description: "Entiendo que los productos químicos pueden causar reacciones alérgicas, irritación o daños al cabello",
      accepted: false
    },
    {
      id: "patch_test",
      title: "Test de parche",
      description: "Confirmo que se ha realizado un test de parche 48 horas antes del servicio o acepto proceder bajo mi responsabilidad",
      accepted: false
    },
    {
      id: "result_expectations",
      title: "Expectativas del resultado",
      description: "Entiendo que el resultado puede variar según el estado actual de mi cabello y acepto las recomendaciones del profesional",
      accepted: false
    },
    {
      id: "aftercare",
      title: "Cuidados posteriores",
      description: "Me comprometo a seguir las instrucciones de cuidado posterior proporcionadas por el estilista",
      accepted: false
    },
    {
      id: "data_processing",
      title: "Tratamiento de datos",
      description: "Acepto el procesamiento temporal de imágenes para análisis IA bajo la política 'Analizar y Descartar'",
      accepted: false
    }
  ]);

  const [signature, setSignature] = useState<string | null>(null);
  const signatureRef = useRef<any>(null);

  // Function to detect client risks
  const detectClientRisks = (clientData: any): string[] => {
    const risks: string[] = [];
    
    if (clientData.knownAllergies) {
      // Check for high severity allergies
      const allergies = clientData.knownAllergies.split(',').map((a: string) => a.trim());
      const highSeverityAllergies = allergies.filter((allergy: string) => {
        return COMMON_ALLERGIES.some(ca => 
          ca.name.toLowerCase().includes(allergy.toLowerCase()) && ca.severity === 'high'
        );
      });
      
      if (highSeverityAllergies.length > 0) {
        risks.push(`⚠️ ALERGIAS DE ALTA SEVERIDAD: ${highSeverityAllergies.join(', ')}`);
      }
      
      const otherAllergies = allergies.filter((a: string) => !highSeverityAllergies.includes(a));
      if (otherAllergies.length > 0) {
        risks.push(`Otras alergias: ${otherAllergies.join(', ')}`);
      }
    }
    if (clientData.pregnancyOrNursing) {
      risks.push("Embarazo o lactancia - Requiere precauciones especiales");
    }
    if (clientData.sensitiveSkin) {
      risks.push("Cuero cabelludo sensible - Test de parche recomendado");
    }
    if (clientData.chemicalTreatments?.henna) {
      risks.push("Historial de henna - Incompatible con procesos químicos");
    }
    if (clientData.chemicalTreatments?.chemicalStraightening) {
      risks.push("Alisado químico previo - Verificar compatibilidad");
    }
    if (clientData.chemicalTreatments?.keratin) {
      risks.push("Tratamiento de keratina - Puede afectar el resultado");
    }
    
    return risks;
  };

  // Load client data
  React.useEffect(() => {
    if (clientId) {
      const foundClient = getClient(clientId as string);
      if (foundClient) {
        setClient(foundClient);
        initializeClientProfile(clientId as string);
        
        // Detect risks
        const risks = detectClientRisks(foundClient);
        setDetectedRisks(risks);
        
        // Pre-fill fields based on client data
        if (foundClient.chemicalTreatments?.henna) {
          setHennaPresence(true);
        }
        if (foundClient.chemicalTreatments?.chemicalStraightening || foundClient.chemicalTreatments?.keratin) {
          setFormaldehydeHistory(true);
        }
        if (foundClient.knownAllergies) {
          setCriticalNotes(`Cliente con alergias conocidas: ${foundClient.knownAllergies}`);
        }
        
        // Check for recent patch test
        const lastPatchTest = getLastPatchTest(clientId as string);
        if (lastPatchTest) {
          const daysSince = Math.floor((Date.now() - new Date(lastPatchTest.date).getTime()) / (1000 * 60 * 60 * 24));
          if (daysSince <= 7 && lastPatchTest.result === 'negativo') {
            setPatchTestCompleted(true);
            setPatchTestResult('negativo');
          }
        }
        
        // If client has sensitive skin or known allergies, suggest patch test
        if (foundClient.sensitiveSkin || foundClient.knownAllergies) {
          // This will be shown as a warning in the patch test step
        }
      }
    }
  }, [clientId, getClient, getLastPatchTest, initializeClientProfile]);

  // Animation values for checkboxes
  const [animationValues] = useState(() => 
    safetyChecks.reduce((acc, item) => {
      acc[item.id] = new Animated.Value(0);
      return acc;
    }, {} as Record<string, Animated.Value>)
  );

  const handleSafetyCheckToggle = (id: string) => {
    setSafetyChecks(prev => {
      const newChecks = prev.map(item => 
        item.id === id ? { ...item, checked: !item.checked } : item
      );
      
      // Animate the checkbox
      const targetValue = newChecks.find(item => item.id === id)?.checked ? 1 : 0;
      Animated.spring(animationValues[id], {
        toValue: targetValue,
        useNativeDriver: true,
        tension: 100,
        friction: 10,
      }).start();
      
      return newChecks;
    });
  };

  const handleConsentToggle = (id: string) => {
    setConsentItems(prev => 
      prev.map(item => 
        item.id === id ? { ...item, accepted: !item.accepted } : item
      )
    );
  };

  const canProceedFromSafety = () => {
    return safetyChecks.filter(item => item.required).every(item => item.checked);
  };

  const canProceedFromPatchTest = () => {
    return patchTestCompleted && (patchTestResult === 'negativo' || patchTestResult === 'pendiente');
  };
  
  const canProceedFromCriticalChecks = () => {
    // Must test for metal salts if there's any chemical history
    const hasChemicalHistory = getClientProfile(clientId as string)?.hairEvolution?.some(
      (evolution: any) => evolution.chemicalProcess !== 'Natural'
    );
    
    if (hasChemicalHistory && metalTestResult === 'not_tested') {
      return false;
    }
    
    // Cannot proceed if metal salts are detected
    if (metalTestResult === 'positive') {
      return false;
    }
    
    // Cannot proceed if henna is present
    if (hennaPresence) {
      return false;
    }
    
    return true;
  };

  const canProceedFromConsent = () => {
    return consentItems.every(item => item.accepted) && signature;
  };

  const nextStep = () => {
    if (currentStep === 0 && !canProceedFromSafety()) {
      Alert.alert(
        "Checklist incompleto",
        "Debes completar todos los elementos obligatorios del checklist de seguridad antes de continuar."
      );
      return;
    }

    if (currentStep === 1 && !canProceedFromPatchTest()) {
      Alert.alert(
        "Test de parche requerido",
        "Es necesario completar el test de parche antes de proceder con el servicio."
      );
      return;
    }

    if (currentStep === 2 && !canProceedFromCriticalChecks()) {
      if (metalTestResult === 'positive') {
        Alert.alert(
          "⚠️ Sales Metálicas Detectadas",
          "No se puede proceder con el servicio. El cabello contiene sales metálicas incompatibles con los oxidantes. Se requiere un tratamiento de eliminación antes de cualquier proceso químico.",
          [{ text: "Entendido" }]
        );
      } else if (hennaPresence) {
        Alert.alert(
          "⚠️ Henna Detectada",
          "La presencia de henna es incompatible con los procesos de coloración química. Se requiere esperar el crecimiento completo del cabello o realizar tratamientos especiales de eliminación.",
          [{ text: "Entendido" }]
        );
      } else {
        Alert.alert(
          "Verificaciones incompletas",
          "Debes completar todas las verificaciones críticas antes de continuar."
        );
      }
      return;
    }

    if (currentStep === 3) {
      if (!canProceedFromConsent()) {
        Alert.alert(
          "Consentimiento incompleto",
          "Debes aceptar todos los términos y proporcionar tu firma antes de continuar."
        );
        return;
      }

      // Save all verification data
      if (clientId) {
        // Save patch test if new
        if (!getLastPatchTest(clientId as string) || patchTestResult !== 'pendiente') {
          addPatchTest(clientId as string, {
            id: Date.now().toString(),
            date: new Date().toISOString(),
            products: ["Productos de coloración estándar"],
            result: patchTestResult,
            notes: patchTestNotes,
            reminderSent: false
          });
        }

        // Save consent record with critical checks
        addConsentRecord(clientId as string, {
          id: Date.now().toString(),
          date: new Date().toISOString(),
          consentItems: consentItems,
          signature: signature!,
          safetyChecklist: safetyChecks,
          criticalChecks: {
            metalTestResult,
            hennaPresence,
            formaldehydeHistory,
            homeRemediesUsed,
            notes: criticalNotes
          },
          ipAddress: "***********", // In real app, get actual IP
          userAgent: "Salonier Mobile App",
          skipSafetyVerification: false
        });
      }

      // Proceed to diagnosis
      router.push(clientId ? `/service/new?clientId=${clientId}` : "/service/new");
      return;
    }

    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  };

  const renderSafetyChecklist = () => {
    const allChecked = safetyChecks.every(item => item.checked);
    const anyChecked = safetyChecks.some(item => item.checked);
    
    const toggleAllChecks = () => {
      if (allChecked) {
        // Deseleccionar todos
        setSafetyChecks(prev => prev.map(item => ({ ...item, checked: false })));
      } else {
        // Seleccionar todos
        setSafetyChecks(prev => prev.map(item => ({ ...item, checked: true })));
      }
    };
    
    return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Shield size={24} color={Colors.light.primary} />
        <Text style={styles.stepTitle}>Verificación de Seguridad</Text>
      </View>
      
      <Text style={styles.stepDescription}>
        Confirma que se cumplen todos los protocolos de seguridad antes de iniciar el servicio
      </Text>
      
      {/* Show detected risks if any */}
      {detectedRisks.length > 0 && (
        <View style={styles.riskAlertContainer}>
          <View style={styles.riskAlertHeader}>
            <AlertTriangle size={20} color={Colors.light.warning} />
            <Text style={styles.riskAlertTitle}>Riesgos Detectados</Text>
          </View>
          {detectedRisks.map((risk, index) => (
            <Text key={index} style={styles.riskAlertItem}>• {risk}</Text>
          ))}
        </View>
      )}
      
      <TouchableOpacity style={styles.selectAllButton} onPress={toggleAllChecks}>
        <View style={[styles.selectAllCheckbox, allChecked && styles.selectAllCheckboxChecked]}>
          {allChecked && <CheckCircle size={16} color="white" />}
        </View>
        <Text style={styles.selectAllText}>
          {allChecked ? 'Deseleccionar todo' : 'Seleccionar todo'}
        </Text>
        {!allChecked && anyChecked && (
          <Text style={styles.selectAllSubtext}>({safetyChecks.filter(item => item.checked).length}/{safetyChecks.length})</Text>
        )}
      </TouchableOpacity>

      <View style={styles.checklistContainer}>
        {safetyChecks.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.checklistItem,
              item.checked && styles.checklistItemChecked,
              item.required && !item.checked && styles.checklistItemRequired
            ]}
            onPress={() => handleSafetyCheckToggle(item.id)}
          >
            <View style={styles.checklistLeft}>
              <View style={[
                styles.checkbox,
                item.checked && styles.checkboxChecked
              ]}>
                {item.checked && <CheckCircle size={20} color="white" />}
              </View>
              <View style={styles.checklistContent}>
                <Text style={[
                  styles.checklistTitle,
                  item.checked && styles.checklistTitleChecked
                ]}>
                  {item.title}
                  {item.required && <Text style={styles.requiredMark}> *</Text>}
                </Text>
                <Text style={styles.checklistDescription}>
                  {item.description}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {allChecked ? (
        <View style={styles.successNote}>
          <CheckCircle size={16} color={Colors.light.success} />
          <Text style={styles.successNoteText}>
            ¡Excelente! Todos los protocolos de seguridad están verificados
          </Text>
        </View>
      ) : (
        <View style={styles.safetyNote}>
          <AlertTriangle size={16} color={Colors.light.warning} />
          <Text style={styles.safetyNoteText}>
            Los elementos marcados con * son obligatorios para garantizar la seguridad del servicio
          </Text>
        </View>
      )}
    </View>
    );
  };

  const renderPatchTest = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Clock size={24} color={Colors.light.accent} />
        <Text style={styles.stepTitle}>Test de Parche</Text>
      </View>
      
      <Text style={styles.stepDescription}>
        Verificación obligatoria para detectar posibles reacciones alérgicas
      </Text>
      
      {/* Show special warning if client has known allergies or sensitive skin */}
      {client && (client.knownAllergies || client.sensitiveSkin) && (
        <View style={styles.specialWarning}>
          <Info size={16} color={Colors.light.warning} />
          <View style={styles.specialWarningContent}>
            <Text style={styles.specialWarningTitle}>Atención Especial Requerida</Text>
            {client.knownAllergies && (() => {
              const allergies = client.knownAllergies.split(',').map((a: string) => a.trim());
              const highSeverityAllergies = allergies.filter((allergy: string) => {
                return COMMON_ALLERGIES.some(ca => 
                  ca.name.toLowerCase().includes(allergy.toLowerCase()) && ca.severity === 'high'
                );
              });
              
              return (
                <>
                  {highSeverityAllergies.length > 0 && (
                    <Text style={[styles.specialWarningText, { color: Colors.light.error, fontWeight: 'bold' }]}>
                      ⚠️ ALERGIAS DE ALTA SEVERIDAD: {highSeverityAllergies.join(', ')}
                    </Text>
                  )}
                  <Text style={styles.specialWarningText}>• Alergias registradas: {client.knownAllergies}</Text>
                </>
              );
            })()}
            {client.sensitiveSkin && (
              <Text style={styles.specialWarningText}>• Cuero cabelludo sensible reportado</Text>
            )}
            <Text style={[styles.specialWarningText, { fontWeight: 'bold', marginTop: 4 }]}>
              Test de parche OBLIGATORIO por seguridad del cliente.
            </Text>
          </View>
        </View>
      )}

      <View style={styles.patchTestContainer}>
        <View style={styles.patchTestOption}>
          <TouchableOpacity
            style={[
              styles.patchTestButton,
              patchTestResult === 'negativo' && styles.patchTestButtonActive
            ]}
            onPress={() => {
              setPatchTestResult('negativo');
              setPatchTestCompleted(true);
            }}
          >
            <CheckCircle size={20} color={patchTestResult === 'negativo' ? "white" : Colors.light.success} />
            <Text style={[
              styles.patchTestButtonText,
              patchTestResult === 'negativo' && styles.patchTestButtonTextActive
            ]}>
              Test Negativo
            </Text>
          </TouchableOpacity>
          <Text style={styles.patchTestDescription}>
            No hay signos de reacción alérgica
          </Text>
        </View>

        <View style={styles.patchTestOption}>
          <TouchableOpacity
            style={[
              styles.patchTestButton,
              styles.patchTestButtonDanger,
              patchTestResult === 'positivo' && styles.patchTestButtonActive
            ]}
            onPress={() => {
              setPatchTestResult('positivo');
              setPatchTestCompleted(true);
              Alert.alert(
                "⚠️ Test Positivo",
                "Se ha detectado una reacción alérgica. No se puede proceder con el servicio. Se recomienda consultar con un dermatólogo.",
                [{ text: "Entendido" }]
              );
            }}
          >
            <AlertTriangle size={20} color={patchTestResult === 'positivo' ? "white" : Colors.light.error} />
            <Text style={[
              styles.patchTestButtonText,
              patchTestResult === 'positivo' && styles.patchTestButtonTextActive
            ]}>
              Test Positivo
            </Text>
          </TouchableOpacity>
          <Text style={styles.patchTestDescription}>
            Hay signos de reacción alérgica
          </Text>
        </View>

        <View style={styles.patchTestOption}>
          <TouchableOpacity
            style={[
              styles.patchTestButton,
              styles.patchTestButtonWarning,
              patchTestResult === 'pendiente' && styles.patchTestButtonActive
            ]}
            onPress={() => {
              setPatchTestResult('pendiente');
              setPatchTestCompleted(true);
              Alert.alert(
                "⚠️ Proceder bajo responsabilidad",
                "El cliente acepta proceder sin test de parche bajo su propia responsabilidad. Esto aumenta el riesgo de reacciones adversas.",
                [{ text: "Entendido" }]
              );
            }}
          >
            <Clock size={20} color={patchTestResult === 'pendiente' ? "white" : Colors.light.warning} />
            <Text style={[
              styles.patchTestButtonText,
              patchTestResult === 'pendiente' && styles.patchTestButtonTextActive
            ]}>
              Sin Test (Bajo Responsabilidad)
            </Text>
          </TouchableOpacity>
          <Text style={styles.patchTestDescription}>
            Cliente acepta proceder sin test previo
          </Text>
        </View>
      </View>

      <View style={styles.patchTestInfo}>
        <Text style={styles.patchTestInfoTitle}>¿Qué es un test de parche?</Text>
        <Text style={styles.patchTestInfoText}>
          Se aplica una pequeña cantidad del producto detrás de la oreja o en el antebrazo 48 horas antes del servicio para detectar posibles reacciones alérgicas.
        </Text>
      </View>
    </View>
  );

  const renderCriticalChecks = () => {
    const clientProfile = getClientProfile(clientId as string);
    const hasChemicalHistory = clientProfile?.hairEvolution?.some(
      (evolution: any) => evolution.chemicalProcess !== 'Natural'
    );
    
    return (
      <View style={styles.stepContainer}>
        <View style={styles.stepHeader}>
          <AlertTriangle size={24} color={Colors.light.error} />
          <Text style={styles.stepTitle}>Verificaciones Críticas de Compatibilidad</Text>
        </View>
        
        <Text style={styles.stepDescription}>
          Estas verificaciones son esenciales para la seguridad del servicio
        </Text>

        {/* Metal Salts Test */}
        <View style={styles.criticalSection}>
          <Text style={styles.criticalSectionTitle}>
            🧪 Test de Sales Metálicas
            {hasChemicalHistory && <Text style={styles.required}> *</Text>}
          </Text>
          
          <View style={styles.metalTestContainer}>
            <Text style={styles.instructionText}>
              Aplicar unas gotas de peróxido (H₂O₂) al 20 vol en un mechón de cabello.
              Observar durante 2-3 minutos:
            </Text>
            
            <View style={styles.testResultButtons}>
              <TouchableOpacity
                style={[
                  styles.testResultButton,
                  metalTestResult === 'negative' && styles.testResultButtonActive
                ]}
                onPress={() => setMetalTestResult('negative')}
              >
                <CheckCircle size={20} color={metalTestResult === 'negative' ? "white" : Colors.light.success} />
                <Text style={[
                  styles.testResultButtonText,
                  metalTestResult === 'negative' && styles.testResultButtonTextActive
                ]}>
                  Sin reacción
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.testResultButton,
                  styles.testResultButtonDanger,
                  metalTestResult === 'positive' && styles.testResultButtonActive
                ]}
                onPress={() => {
                  setMetalTestResult('positive');
                  Alert.alert(
                    "⚠️ Sales Metálicas Detectadas",
                    "Signos de reacción:\n• Burbujeo excesivo\n• Calentamiento del mechón\n• Cambio de color a verde/negro\n• Olor metálico\n\nNO PROCEDER con el servicio.",
                    [{ text: "Entendido" }]
                  );
                }}
              >
                <AlertTriangle size={20} color={metalTestResult === 'positive' ? "white" : Colors.light.error} />
                <Text style={[
                  styles.testResultButtonText,
                  metalTestResult === 'positive' && styles.testResultButtonTextActive
                ]}>
                  Reacción detectada
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Henna Check */}
        <View style={styles.criticalSection}>
          <Text style={styles.criticalSectionTitle}>🌿 Historial de Henna</Text>
          <TouchableOpacity 
            style={[styles.checkItem, hennaPresence && styles.checkItemDanger]}
            onPress={() => setHennaPresence(!hennaPresence)}
          >
            <View style={[styles.checkbox, hennaPresence && styles.checkboxDanger]}>
              {hennaPresence && <AlertTriangle size={16} color="white" />}
            </View>
            <Text style={styles.checkItemText}>
              ¿Ha usado henna o tintes vegetales en los últimos 2 años?
            </Text>
          </TouchableOpacity>
        </View>

        {/* Formaldehyde Check */}
        <View style={styles.criticalSection}>
          <Text style={styles.criticalSectionTitle}>💨 Tratamientos con Formol</Text>
          <TouchableOpacity 
            style={[styles.checkItem, formaldehydeHistory && styles.checkItemWarning]}
            onPress={() => setFormaldehydeHistory(!formaldehydeHistory)}
          >
            <View style={[styles.checkbox, formaldehydeHistory && styles.checkboxWarning]}>
              {formaldehydeHistory && <AlertTriangle size={16} color="white" />}
            </View>
            <Text style={styles.checkItemText}>
              ¿Ha realizado alisados con formol o keratina?
            </Text>
          </TouchableOpacity>
        </View>

        {/* Home Remedies Check */}
        <View style={styles.criticalSection}>
          <Text style={styles.criticalSectionTitle}>🏠 Remedios Caseros</Text>
          <TouchableOpacity 
            style={[styles.checkItem]}
            onPress={() => setHomeRemediesUsed(!homeRemediesUsed)}
          >
            <View style={[styles.checkbox, homeRemediesUsed && styles.checkboxChecked]}>
              {homeRemediesUsed && <CheckCircle size={16} color="white" />}
            </View>
            <Text style={styles.checkItemText}>
              ¿Ha usado limón, manzanilla, vinagre u otros remedios caseros?
            </Text>
          </TouchableOpacity>
        </View>

        {/* Notes */}
        <View style={styles.notesSection}>
          <Text style={styles.notesLabel}>Observaciones adicionales:</Text>
          <TextInput
            style={styles.notesInput}
            value={criticalNotes}
            onChangeText={setCriticalNotes}
            placeholder="Anotar cualquier detalle relevante..."
            multiline
            numberOfLines={3}
          />
        </View>
        
        {/* Show client history summary if available */}
        {client && (client.knownAllergies || client.pregnancyOrNursing || Object.values(client.chemicalTreatments || {}).some(v => v)) && (
          <View style={styles.clientHistorySummary}>
            <Text style={styles.clientHistoryTitle}>📋 Información del Cliente</Text>
            {client.knownAllergies && (
              <Text style={styles.clientHistoryItem}>• Alergias: {client.knownAllergies}</Text>
            )}
            {client.pregnancyOrNursing && (
              <Text style={styles.clientHistoryItem}>• Estado: Embarazo/Lactancia</Text>
            )}
            {client.chemicalTreatments?.henna && (
              <Text style={styles.clientHistoryItem}>• Tratamiento previo: Henna</Text>
            )}
            {client.chemicalTreatments?.chemicalStraightening && (
              <Text style={styles.clientHistoryItem}>• Tratamiento previo: Alisado químico</Text>
            )}
            {client.chemicalTreatments?.keratin && (
              <Text style={styles.clientHistoryItem}>• Tratamiento previo: Keratina/Botox</Text>
            )}
          </View>
        )}

        {/* Risk Summary */}
        {(metalTestResult === 'positive' || hennaPresence) && (
          <View style={styles.riskSummary}>
            <AlertTriangle size={20} color={Colors.light.error} />
            <Text style={styles.riskSummaryText}>
              {metalTestResult === 'positive' && "Sales metálicas detectadas. "}
              {hennaPresence && "Presencia de henna. "}
              No se puede proceder con el servicio.
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderConsent = () => {
    const allAccepted = consentItems.every(item => item.accepted);
    const anyAccepted = consentItems.some(item => item.accepted);
    
    const toggleAllConsents = () => {
      if (allAccepted) {
        // Deseleccionar todos
        setConsentItems(prev => prev.map(item => ({ ...item, accepted: false })));
      } else {
        // Seleccionar todos
        setConsentItems(prev => prev.map(item => ({ ...item, accepted: true })));
      }
    };
    
    return (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <FileText size={24} color={Colors.light.success} />
        <Text style={styles.stepTitle}>Consentimiento Informado</Text>
      </View>
      
      <Text style={styles.stepDescription}>
        Lee y acepta los términos del servicio antes de proceder
      </Text>
      
      <TouchableOpacity style={styles.selectAllButton} onPress={toggleAllConsents}>
        <View style={[styles.selectAllCheckbox, allAccepted && styles.selectAllCheckboxChecked]}>
          {allAccepted && <CheckCircle size={16} color="white" />}
        </View>
        <Text style={styles.selectAllText}>
          {allAccepted ? 'Todos aceptados' : 'Aceptar todos los términos'}
        </Text>
        {!allAccepted && anyAccepted && (
          <Text style={styles.selectAllSubtext}>({consentItems.filter(item => item.accepted).length}/{consentItems.length})</Text>
        )}
      </TouchableOpacity>

      <View style={styles.consentListContainer}>
        <ScrollView 
          style={styles.consentScrollView} 
          showsVerticalScrollIndicator={true}
          contentContainerStyle={styles.consentContentContainer}
        >
          {consentItems.map((item) => (
            <TouchableOpacity 
              key={item.id} 
              style={[styles.consentItem, item.accepted && styles.consentItemAccepted]}
              onPress={() => handleConsentToggle(item.id)}
              activeOpacity={0.7}
            >
              <View style={styles.consentHeader}>
                <Switch
                  value={item.accepted}
                  onValueChange={() => handleConsentToggle(item.id)}
                  trackColor={{ false: Colors.light.lightGray, true: Colors.light.primary }}
                  thumbColor={item.accepted ? "white" : Colors.light.gray}
                />
                <Text style={[styles.consentTitle, item.accepted && styles.consentTitleAccepted]}>{item.title}</Text>
              </View>
              <Text style={styles.consentDescription}>{item.description}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        {!allAccepted && (
          <View style={styles.scrollIndicator}>
            <Text style={styles.scrollIndicatorText}>↓ Desliza para ver más términos</Text>
          </View>
        )}
      </View>

      <View style={styles.signatureSectionContainer}>
        <View style={styles.signatureHeader}>
          <Text style={styles.signatureTitle}>Firma del Cliente</Text>
          {signature && (
            <View style={styles.signatureStatus}>
              <CheckCircle size={16} color={Colors.light.success} />
              <Text style={styles.signatureStatusText}>Firmado</Text>
            </View>
          )}
        </View>
        <View style={styles.signatureCanvasWrapper}>
          <SignatureCanvas
            ref={signatureRef}
            onSignature={setSignature}
            style={styles.signatureCanvas}
          />
        </View>
        <TouchableOpacity
          style={styles.clearSignatureButton}
          onPress={() => {
            signatureRef.current?.clear();
            setSignature(null);
          }}
        >
          <Text style={styles.clearSignatureText}>Limpiar Firma</Text>
        </TouchableOpacity>
      </View>

      {client && (
        <View style={styles.clientConfirmation}>
          <Text style={styles.clientConfirmationText}>
            Yo, <Text style={styles.clientName}>{client.name}</Text>, confirmo que he leído y acepto todos los términos mencionados anteriormente.
          </Text>
          <Text style={styles.dateText}>
            Fecha: {new Date().toLocaleDateString()}
          </Text>
        </View>
      )}
    </View>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderSafetyChecklist();
      case 1:
        return renderPatchTest();
      case 2:
        return renderCriticalChecks();
      case 3:
        return renderConsent();
      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 0: return "Verificación de Seguridad";
      case 1: return "Test de Parche";
      case 2: return "Verificaciones Críticas";
      case 3: return "Consentimiento";
      default: return "";
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: return canProceedFromSafety();
      case 1: return canProceedFromPatchTest() && patchTestResult !== 'positivo';
      case 2: return canProceedFromCriticalChecks();
      case 3: return canProceedFromConsent();
      default: return false;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={prevStep}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{getStepTitle()}</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.progressContainer}>
        {[0, 1, 2, 3].map((step) => (
          <React.Fragment key={step}>
            <View 
              style={[
                styles.progressStep, 
                step <= currentStep ? styles.progressStepActive : {}
              ]}
            >
              <Text style={styles.progressStepText}>{step + 1}</Text>
            </View>
            {step < 3 && (
              <View 
                style={[
                  styles.progressLine, 
                  step < currentStep ? styles.progressLineActive : {}
                ]} 
              />
            )}
          </React.Fragment>
        ))}
      </View>

      <ScrollView style={styles.scrollContainer}>
        {renderCurrentStep()}

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={styles.backStepButton} 
            onPress={prevStep}
          >
            <Text style={styles.backStepButtonText}>
              {currentStep === 0 ? "Cancelar" : "Anterior"}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[
              styles.nextStepButton,
              !canProceed() && styles.nextStepButtonDisabled
            ]}
            onPress={nextStep}
            disabled={!canProceed()}
          >
            <Text style={[
              styles.nextStepButtonText,
              !canProceed() && styles.nextStepButtonTextDisabled
            ]}>
              {currentStep === 3 ? "Iniciar Diagnóstico" : "Siguiente"}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  placeholder: {
    width: 34,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 15,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  progressStep: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.light.lightGray,
    justifyContent: "center",
    alignItems: "center",
  },
  progressStepActive: {
    backgroundColor: Colors.light.primary,
  },
  progressStepText: {
    color: "white",
    fontWeight: "bold",
  },
  progressLine: {
    height: 2,
    width: 30,
    backgroundColor: Colors.light.lightGray,
  },
  progressLineActive: {
    backgroundColor: Colors.light.primary,
  },
  scrollContainer: {
    flex: 1,
  },
  stepContainer: {
    padding: 20,
  },
  stepHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    gap: 12,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: Colors.light.text,
  },
  stepDescription: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 24,
    lineHeight: 22,
  },
  checklistContainer: {
    gap: 12,
    marginBottom: 20,
  },
  checklistItem: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  checklistItemChecked: {
    borderColor: Colors.light.success,
    backgroundColor: Colors.light.success + "05",
  },
  checklistItemRequired: {
    borderColor: Colors.light.warning + "50",
  },
  checklistLeft: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  checklistContent: {
    flex: 1,
  },
  checklistTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 4,
  },
  checklistTitleChecked: {
    color: Colors.light.success,
  },
  checklistDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
  requiredMark: {
    color: Colors.light.warning,
  },
  selectAllButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    gap: 8,
  },
  selectAllCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  selectAllCheckboxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  selectAllText: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  selectAllSubtext: {
    fontSize: 14,
    color: Colors.light.gray,
    marginLeft: 4,
  },
  safetyNote: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.warning + "10",
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  safetyNoteText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.warning,
    fontWeight: "500",
  },
  successNote: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.success + "10",
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  successNoteText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.success,
    fontWeight: "500",
  },
  patchTestContainer: {
    gap: 16,
    marginBottom: 24,
  },
  patchTestOption: {
    alignItems: "center",
  },
  patchTestButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderColor: Colors.light.border,
    gap: 12,
    minWidth: 250,
    justifyContent: "center",
  },
  patchTestButtonActive: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  patchTestButtonDanger: {
    borderColor: Colors.light.error + "50",
  },
  patchTestButtonWarning: {
    borderColor: Colors.light.warning + "50",
  },
  patchTestButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  patchTestButtonTextActive: {
    color: "white",
  },
  patchTestDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: "center",
    marginTop: 8,
    maxWidth: 250,
  },
  patchTestInfo: {
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 12,
    padding: 16,
  },
  patchTestInfoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.primary,
    marginBottom: 8,
  },
  patchTestInfoText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  consentListContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    marginBottom: 24,
    maxHeight: 350,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  consentScrollView: {
    paddingHorizontal: 12,
    paddingTop: 12,
  },
  consentContentContainer: {
    paddingBottom: 12,
  },
  scrollIndicator: {
    backgroundColor: Colors.light.primary + "10",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    alignItems: "center",
  },
  scrollIndicatorText: {
    fontSize: 12,
    color: Colors.light.primary,
    fontWeight: "500",
  },
  consentItem: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 0,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  consentItemAccepted: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + "05",
  },
  consentHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    gap: 12,
  },
  consentTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    flex: 1,
  },
  consentTitleAccepted: {
    color: Colors.light.primary,
  },
  consentDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
  signatureSectionContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  signatureHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  signatureStatus: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.success + "15",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 4,
  },
  signatureStatusText: {
    fontSize: 12,
    color: Colors.light.success,
    fontWeight: "600",
  },
  signatureTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
  },
  signatureCanvasWrapper: {
    width: "100%",
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 2,
    borderColor: Colors.light.border,
    marginBottom: 16,
  },
  signatureCanvas: {
    width: "100%",
    height: 200,
    backgroundColor: Colors.light.surface,
  },
  clearSignatureButton: {
    alignSelf: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  clearSignatureText: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: "500",
  },
  clientConfirmation: {
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 12,
    padding: 16,
  },
  clientConfirmationText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  clientName: {
    fontWeight: "600",
    color: Colors.light.primary,
  },
  dateText: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  backStepButton: {
    flex: 1,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginRight: 12,
  },
  backStepButtonText: {
    color: Colors.light.text,
    fontWeight: "600",
    fontSize: 16,
  },
  nextStepButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
  },
  nextStepButtonDisabled: {
    backgroundColor: Colors.light.lightGray,
  },
  nextStepButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  nextStepButtonTextDisabled: {
    color: Colors.light.gray,
  },
  criticalSection: {
    marginBottom: 24,
  },
  criticalSectionTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 12,
  },
  metalTestContainer: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
    marginBottom: 16,
  },
  testResultButtons: {
    flexDirection: "row",
    gap: 12,
  },
  testResultButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderWidth: 2,
    borderColor: Colors.light.border,
    gap: 8,
  },
  testResultButtonActive: {
    backgroundColor: Colors.light.success,
    borderColor: Colors.light.success,
  },
  testResultButtonDanger: {
    borderColor: Colors.light.error + "50",
  },
  testResultButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.light.text,
    flex: 1,
    textAlign: "center",
  },
  testResultButtonTextActive: {
    color: "white",
  },
  checkItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    gap: 12,
  },
  checkItemDanger: {
    borderColor: Colors.light.error,
    backgroundColor: Colors.light.error + "05",
  },
  checkItemWarning: {
    borderColor: Colors.light.warning,
    backgroundColor: Colors.light.warning + "05",
  },
  checkboxDanger: {
    backgroundColor: Colors.light.error,
    borderColor: Colors.light.error,
  },
  checkboxWarning: {
    backgroundColor: Colors.light.warning,
    borderColor: Colors.light.warning,
  },
  checkItemText: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.text,
    lineHeight: 22,
  },
  notesSection: {
    marginTop: 20,
  },
  notesLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginBottom: 8,
  },
  notesInput: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    fontSize: 15,
    color: Colors.light.text,
    minHeight: 80,
    textAlignVertical: "top",
  },
  riskSummary: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.error + "10",
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    gap: 12,
  },
  riskSummaryText: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.error,
    fontWeight: "600",
    lineHeight: 22,
  },
  riskAlertContainer: {
    backgroundColor: Colors.light.warning + "10",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.warning + "30",
  },
  riskAlertHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 12,
  },
  riskAlertTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.warning,
  },
  riskAlertItem: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginBottom: 4,
  },
  specialWarning: {
    flexDirection: "row",
    backgroundColor: Colors.light.warning + "10",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    gap: 12,
    alignItems: "flex-start",
  },
  specialWarningContent: {
    flex: 1,
  },
  specialWarningTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.light.warning,
    marginBottom: 8,
  },
  specialWarningText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginBottom: 2,
  },
  clientHistorySummary: {
    backgroundColor: Colors.light.primary + "10",
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  clientHistoryTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.light.primary,
    marginBottom: 8,
  },
  clientHistoryItem: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginBottom: 2,
  },
});