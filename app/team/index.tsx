import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { Plus, Users, Phone, Mail, Award, MoreVertical } from 'lucide-react-native';
import { BaseHeader } from '@/components/base';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { useTeamStore } from '@/stores/team-store';
import { TeamMember } from '@/types/team';

export default function TeamScreen() {
  const { members, toggleMemberStatus, removeMember } = useTeamStore();
  const [selectedMember, setSelectedMember] = useState<string | null>(null);

  const handleAddMember = () => {
    router.push('/team/new');
  };

  const handleEditMember = (memberId: string) => {
    router.push(`/team/edit/${memberId}`);
  };

  const handleToggleStatus = (memberId: string) => {
    toggleMemberStatus(memberId);
  };

  const handleRemoveMember = (member: TeamMember) => {
    Alert.alert(
      'Eliminar Miembro',
      `¿Estás seguro de que deseas eliminar a ${member.name} del equipo?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => removeMember(member.id),
        },
      ]
    );
  };

  const getRoleColor = (role: TeamMember['role']) => {
    switch (role) {
      case 'Colorista':
        return Colors.light.primary;
      case 'Asistente':
        return Colors.light.secondary;
      case 'Estilista':
        return Colors.light.accent;
      case 'Recepcionista':
        return Colors.light.info;
      case 'Manager':
        return Colors.light.warning;
      default:
        return Colors.light.gray;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <View style={styles.container}>
      <BaseHeader 
        title="Mi Equipo" 
        showBack 
        onBack={() => router.back()}
        rightAction={{
          icon: Plus,
          onPress: handleAddMember,
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {members.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Users size={64} color={Colors.light.lightGray} />
            <Text style={styles.emptyTitle}>No hay miembros en tu equipo</Text>
            <Text style={styles.emptyDescription}>
              Añade a los miembros de tu equipo para gestionar permisos y accesos
            </Text>
            <TouchableOpacity style={styles.addButton} onPress={handleAddMember}>
              <Plus size={20} color="white" />
              <Text style={styles.addButtonText}>Añadir Miembro</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.membersList}>
            {members.map((member) => (
              <TouchableOpacity
                key={member.id}
                style={styles.memberCard}
                onPress={() => handleEditMember(member.id)}
                activeOpacity={0.7}
              >
                <View style={styles.memberHeader}>
                  <View style={styles.memberInfo}>
                    <View style={[styles.avatar, { backgroundColor: getRoleColor(member.role) + '20' }]}>
                      <Text style={[styles.avatarText, { color: getRoleColor(member.role) }]}>
                        {getInitials(member.name)}
                      </Text>
                    </View>
                    <View style={styles.memberDetails}>
                      <Text style={styles.memberName}>{member.name}</Text>
                      <View style={styles.roleContainer}>
                        <View style={[styles.roleBadge, { backgroundColor: getRoleColor(member.role) + '15' }]}>
                          <Text style={[styles.roleText, { color: getRoleColor(member.role) }]}>
                            {member.role}
                          </Text>
                        </View>
                        {member.licenseNumber && (
                          <View style={styles.licenseContainer}>
                            <Award size={12} color={Colors.light.gray} />
                            <Text style={styles.licenseText}>{member.licenseNumber}</Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                  <View style={styles.memberActions}>
                    <Switch
                      value={member.activeStatus}
                      onValueChange={() => handleToggleStatus(member.id)}
                      trackColor={{ false: Colors.light.lightGray, true: Colors.light.success }}
                      thumbColor="white"
                    />
                    <TouchableOpacity
                      style={styles.moreButton}
                      onPress={() => setSelectedMember(member.id === selectedMember ? null : member.id)}
                    >
                      <MoreVertical size={20} color={Colors.light.gray} />
                    </TouchableOpacity>
                  </View>
                </View>

                {selectedMember === member.id && (
                  <View style={styles.memberMenu}>
                    <TouchableOpacity
                      style={styles.menuItem}
                      onPress={() => handleEditMember(member.id)}
                    >
                      <Text style={styles.menuItemText}>Editar</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.menuItem, styles.menuItemDanger]}
                      onPress={() => handleRemoveMember(member)}
                    >
                      <Text style={[styles.menuItemText, styles.menuItemTextDanger]}>
                        Eliminar
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}

                {(member.email || member.phone) && (
                  <View style={styles.contactInfo}>
                    {member.email && (
                      <View style={styles.contactItem}>
                        <Mail size={14} color={Colors.light.gray} />
                        <Text style={styles.contactText}>{member.email}</Text>
                      </View>
                    )}
                    {member.phone && (
                      <View style={styles.contactItem}>
                        <Phone size={14} color={Colors.light.gray} />
                        <Text style={styles.contactText}>{member.phone}</Text>
                      </View>
                    )}
                  </View>
                )}

                {member.specializations && member.specializations.length > 0 && (
                  <View style={styles.specializationsContainer}>
                    {member.specializations.map((spec, index) => (
                      <View key={index} style={styles.specializationTag}>
                        <Text style={styles.specializationText}>{spec}</Text>
                      </View>
                    ))}
                  </View>
                )}

                <View style={styles.permissionsInfo}>
                  <Text style={styles.permissionsTitle}>Permisos:</Text>
                  <View style={styles.permissionsList}>
                    {member.permissions?.canCreateServices && (
                      <Text style={styles.permissionItem}>• Crear servicios</Text>
                    )}
                    {member.permissions?.canAccessInventory && (
                      <Text style={styles.permissionItem}>• Acceder al inventario</Text>
                    )}
                    {member.permissions?.canViewReports && (
                      <Text style={styles.permissionItem}>• Ver reportes</Text>
                    )}
                    {member.permissions?.canManageClients && (
                      <Text style={styles.permissionItem}>• Gestionar clientes</Text>
                    )}
                  </View>
                </View>

                {!member.activeStatus && (
                  <View style={styles.inactiveOverlay}>
                    <Text style={styles.inactiveText}>INACTIVO</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xxxl * 2,
  },
  emptyTitle: {
    ...typography.h3,
    color: Colors.light.text,
    marginTop: spacing.lg,
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body,
    color: Colors.light.gray,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: radius.full,
    marginTop: spacing.xl,
    gap: spacing.sm,
  },
  addButtonText: {
    ...typography.buttonMedium,
    color: 'white',
  },
  membersList: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  memberCard: {
    backgroundColor: 'white',
    borderRadius: radius.lg,
    padding: spacing.lg,
    ...shadows.sm,
    position: 'relative',
  },
  memberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  memberInfo: {
    flexDirection: 'row',
    flex: 1,
    gap: spacing.md,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    ...typography.h4,
    fontWeight: '600',
  },
  memberDetails: {
    flex: 1,
    gap: spacing.xs,
  },
  memberName: {
    ...typography.h4,
    color: Colors.light.text,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  roleBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
  },
  roleText: {
    ...typography.caption,
    fontWeight: '600',
  },
  licenseContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  licenseText: {
    ...typography.caption,
    color: Colors.light.gray,
  },
  memberActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  moreButton: {
    padding: spacing.xs,
  },
  memberMenu: {
    marginTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.lightGray,
    paddingTop: spacing.md,
    gap: spacing.sm,
  },
  menuItem: {
    paddingVertical: spacing.sm,
  },
  menuItemDanger: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.lightGray,
  },
  menuItemText: {
    ...typography.body,
    color: Colors.light.text,
  },
  menuItemTextDanger: {
    color: Colors.light.danger,
  },
  contactInfo: {
    marginTop: spacing.md,
    gap: spacing.xs,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  contactText: {
    ...typography.caption,
    color: Colors.light.gray,
  },
  specializationsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    marginTop: spacing.md,
  },
  specializationTag: {
    backgroundColor: Colors.light.background,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
  },
  specializationText: {
    ...typography.caption,
    color: Colors.light.gray,
  },
  permissionsInfo: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.lightGray,
  },
  permissionsTitle: {
    ...typography.caption,
    color: Colors.light.gray,
    marginBottom: spacing.xs,
  },
  permissionsList: {
    gap: spacing.xs,
  },
  permissionItem: {
    ...typography.caption,
    color: Colors.light.text,
  },
  inactiveOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: radius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inactiveText: {
    ...typography.h3,
    color: Colors.light.gray,
    fontWeight: '700',
    letterSpacing: 2,
  },
});