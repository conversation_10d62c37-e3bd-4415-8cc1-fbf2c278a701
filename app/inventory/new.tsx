import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TextInput, TouchableOpacity, ScrollView, KeyboardAvoidingView, Platform, Alert } from "react-native";
import { router } from "expo-router";
import { ChevronLeft, Sparkles, ChevronDown } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useInventoryStore } from "@/stores/inventory-store";
import { Product } from "@/types/inventory";
import { useRegionalUnits } from "@/hooks/useRegionalUnits";

export default function NewInventoryItemScreen() {
  const { addProduct, searchProducts } = useInventoryStore();
  const { volumeUnit, weightUnit, toBaseVolume, toBaseWeight, isMetric, developerTerm, colorTerm, formatCurrency } = useRegionalUnits();
  
  const [brand, setBrand] = useState("");
  const [productType, setProductType] = useState("");
  const [line, setLine] = useState("");
  const [productName, setProductName] = useState("");
  const [packageCount, setPackageCount] = useState("");
  const [packageSize, setPackageSize] = useState("");
  const [totalStock, setTotalStock] = useState("");
  const [unit, setUnit] = useState(volumeUnit);
  const [inputMode, setInputMode] = useState<'packages' | 'total'>('packages');
  const [lowStockThreshold, setLowStockThreshold] = useState("");
  const [barcode, setBarcode] = useState("");
  const [purchasePrice, setPurchasePrice] = useState("");
  const [category, setCategory] = useState<Product['category']>('otro');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [aiInput, setAiInput] = useState("");
  
  // Calculate total stock when packages or size changes
  useEffect(() => {
    if (inputMode === 'packages' && packageCount && packageSize) {
      const count = parseFloat(packageCount);
      const size = parseFloat(packageSize);
      if (!isNaN(count) && !isNaN(size)) {
        setTotalStock((count * size).toString());
      }
    }
  }, [packageCount, packageSize, inputMode]);
  
  const categories: Array<{ value: Product['category']; label: string }> = [
    { value: 'tinte', label: colorTerm.charAt(0).toUpperCase() + colorTerm.slice(1) },
    { value: 'oxidante', label: developerTerm.charAt(0).toUpperCase() + developerTerm.slice(1) },
    { value: 'decolorante', label: 'Decolorante' },
    { value: 'tratamiento', label: 'Tratamiento' },
    { value: 'otro', label: 'Otro' },
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!brand.trim()) {
      newErrors.brand = "La marca es obligatoria";
    }
    
    if (!productType.trim()) {
      newErrors.productType = "El tipo de producto es obligatorio";
    }
    
    if (inputMode === 'packages') {
      if (!packageCount.trim() || parseFloat(packageCount) <= 0) {
        newErrors.packageCount = "La cantidad de envases es obligatoria";
      }
      if (!packageSize.trim() || parseFloat(packageSize) <= 0) {
        newErrors.packageSize = "El tamaño del envase es obligatorio";
      }
    } else {
      if (!totalStock.trim() || parseFloat(totalStock) <= 0) {
        newErrors.totalStock = "La cantidad total es obligatoria";
      }
    }
    
    if (!purchasePrice.trim() || parseFloat(purchasePrice) <= 0) {
      newErrors.purchasePrice = "El precio de compra es obligatorio";
    }
    
    // Stock validation is handled above based on input mode
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      // Crear mensaje con los campos faltantes
      const errorMessages = [];
      if (errors.brand) errorMessages.push("• Marca");
      if (errors.productType) errorMessages.push("• Tipo de Producto");
      if (errors.packageCount) errorMessages.push("• Cantidad de Envases");
      if (errors.packageSize) errorMessages.push("• Tamaño del Envase");
      if (errors.totalStock) errorMessages.push("• Cantidad Total");
      if (errors.purchasePrice) errorMessages.push("• Precio de Compra");
      
      Alert.alert(
        "Campos Requeridos",
        "Por favor complete los siguientes campos:\n\n" + errorMessages.join("\n"),
        [{ text: "OK" }]
      );
      
      // Scroll al primer campo con error
      // Note: This is a simplified approach. In a real app, you might use refs
      return;
    }

    setIsLoading(true);

    try {
      // Check for duplicates
      const existingProducts = searchProducts(`${brand} ${productType} ${productName}`.trim());
      if (existingProducts.length > 0) {
        const duplicate = existingProducts.find(p => 
          p.brand.toLowerCase() === brand.toLowerCase() &&
          p.name.toLowerCase().includes(productType.toLowerCase())
        );
        
        if (duplicate) {
          Alert.alert(
            "Producto Duplicado",
            `Ya existe un producto similar: ${duplicate.brand} ${duplicate.name}. ¿Deseas continuar?`,
            [
              { text: "Cancelar", style: "cancel" },
              { text: "Continuar", onPress: () => saveProduct() }
            ]
          );
          return;
        }
      }
      
      await saveProduct();
    } catch (error) {
      console.error("Error saving inventory item:", error);
      Alert.alert("Error", "No se pudo guardar el producto");
    } finally {
      setIsLoading(false);
    }
  };
  
  const saveProduct = async () => {
    const productDisplayName = [
      productType,
      line,
      productName
    ].filter(Boolean).join(' ').trim();
    
    // Calculate total stock and unit size based on input mode
    let finalTotalStock = inputMode === 'packages' 
      ? parseFloat(packageCount) * parseFloat(packageSize)
      : parseFloat(totalStock);
    
    let finalUnitSize = inputMode === 'packages'
      ? parseFloat(packageSize)
      : parseFloat(totalStock); // For direct input, unit size equals total
    
    // Convert to base units if necessary
    if (unit === volumeUnit && !isMetric) {
      // Convert fl oz to ml
      finalTotalStock = toBaseVolume(finalTotalStock);
      finalUnitSize = toBaseVolume(finalUnitSize);
    } else if (unit === weightUnit && !isMetric) {
      // Convert oz to g
      finalTotalStock = toBaseWeight(finalTotalStock);
      finalUnitSize = toBaseWeight(finalUnitSize);
    }
    
    const costPerUnit = parseFloat(purchasePrice) / finalTotalStock;
    
    // Store always in base units (ml/g) but keep track of original unit type
    const baseUnitType = unit === volumeUnit ? 'ml' : unit === weightUnit ? 'g' : 'unidad';
    
    await addProduct({
      name: productDisplayName || productType,
      brand: brand.trim(),
      category,
      currentStock: finalTotalStock,
      minStock: parseFloat(lowStockThreshold) || 0,
      unitType: baseUnitType as 'ml' | 'g' | 'unidad',
      unitSize: finalUnitSize,
      purchasePrice: parseFloat(purchasePrice),
      costPerUnit,
      isActive: true,
      barcode: barcode.trim() || undefined,
    });
    
    router.push("/inventory");
  };
  
  const analyzeAIInput = () => {
    if (!aiInput.trim()) return;
    
    // Simple parsing logic - can be enhanced
    const input = aiInput.toLowerCase();
    
    // Try to extract brand (common brands)
    const brands = ['wella', 'loreal', 'schwarzkopf', 'revlon', 'koleston', 'igora', 'majirel'];
    const foundBrand = brands.find(b => input.includes(b));
    if (foundBrand) {
      setBrand(foundBrand.charAt(0).toUpperCase() + foundBrand.slice(1));
    }
    
    // Try to extract category (check for regional terminology)
    if (input.includes(colorTerm.toLowerCase()) || input.includes('color') || input.includes('tinte')) {
      setCategory('tinte');
      setProductType(colorTerm.charAt(0).toUpperCase() + colorTerm.slice(1));
    } else if (input.includes(developerTerm.toLowerCase()) || input.includes('oxidante') || input.includes('peróxido') || input.includes('developer')) {
      setCategory('oxidante');
      setProductType(developerTerm.charAt(0).toUpperCase() + developerTerm.slice(1));
    } else if (input.includes('decolorante') || input.includes('polvo') || input.includes('bleach')) {
      setCategory('decolorante');
      setProductType('Decolorante');
    } else if (input.includes('tratamiento') || input.includes('olaplex') || input.includes('treatment')) {
      setCategory('tratamiento');
      setProductType('Tratamiento');
    }
    
    // Try to extract size and quantity (support both metric and imperial)
    const sizeMatch = input.match(/(\d+\.?\d*)\s*(ml|g|fl\s?oz|oz)/i);
    if (sizeMatch) {
      setPackageSize(sizeMatch[1]);
      const matchedUnit = sizeMatch[2].toLowerCase().replace(/\s/g, '');
      
      // Map to appropriate unit
      if (matchedUnit === 'ml' || matchedUnit === 'floz') {
        setUnit(volumeUnit);
      } else if (matchedUnit === 'g' || matchedUnit === 'oz') {
        setUnit(weightUnit);
      }
      
      // Default to 1 package if not specified
      if (!packageCount) {
        setPackageCount('1');
      }
    }
    
    // Try to extract quantity (e.g., "5 tubos", "10 unidades")
    const quantityMatch = input.match(/(\d+)\s*(tubos?|unidades?|envases?)/i);
    if (quantityMatch) {
      setPackageCount(quantityMatch[1]);
    }
    
    // Try to extract tone/reference
    const toneMatch = input.match(/(\d+[/\\]\d+)|([\d.]+)/g);
    if (toneMatch && toneMatch.length > 0) {
      setProductName(toneMatch[0]);
    }
    
    Alert.alert("Análisis Completado", "Revisa y completa los campos detectados");
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Añadir Nuevo Artículo</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.formContainer}>
          <View style={styles.aiSection}>
            <View style={styles.aiHeader}>
              <Sparkles size={20} color={Colors.light.primary} />
              <Text style={styles.aiTitle}>Entrada Rápida con IA (Opcional)</Text>
            </View>
            <TextInput
              style={[styles.input, styles.aiInput]}
              placeholder="Describe el producto (ej: 'Tinte Wella Koleston 8/0 60ml')"
              value={aiInput}
              onChangeText={setAiInput}
            />
            <TouchableOpacity style={styles.aiButton} onPress={analyzeAIInput}>
              <Text style={styles.aiButtonText}>Analizar</Text>
            </TouchableOpacity>
          </View>

          <Text style={styles.sectionTitle}>Información del Artículo</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Marca *</Text>
            <TextInput
              style={[styles.input, errors.brand && styles.inputError]}
              value={brand}
              onChangeText={setBrand}
              placeholder="Ej: Wella Professionals"
            />
            {errors.brand && <Text style={styles.errorText}>{errors.brand}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Categoría *</Text>
            <TouchableOpacity 
              style={[styles.input, styles.pickerInput]}
              onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            >
              <Text style={styles.pickerText}>
                {categories.find(c => c.value === category)?.label || 'Seleccionar'}
              </Text>
              <ChevronDown size={20} color={Colors.light.text} />
            </TouchableOpacity>
            {showCategoryPicker && (
              <View style={styles.pickerOptions}>
                {categories.map((cat) => (
                  <TouchableOpacity
                    key={cat.value}
                    style={styles.pickerOption}
                    onPress={() => {
                      setCategory(cat.value);
                      setShowCategoryPicker(false);
                    }}
                  >
                    <Text style={[
                      styles.pickerOptionText,
                      category === cat.value && styles.pickerOptionTextSelected
                    ]}>
                      {cat.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Tipo de Producto *</Text>
            <TextInput
              style={[styles.input, errors.productType && styles.inputError]}
              value={productType}
              onChangeText={setProductType}
              placeholder="Ej: Tinte, Oxidante, Champú"
            />
            {errors.productType && <Text style={styles.errorText}>{errors.productType}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Línea (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={line}
              onChangeText={setLine}
              placeholder="Ej: Illumina Color, Koleston Perfect"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Nombre Producto (con Tono/Ref)</Text>
            <TextInput
              style={styles.input}
              value={productName}
              onChangeText={setProductName}
              placeholder="Ej: Koleston 8/0 Rubio Claro"
            />
          </View>

          {/* Stock Input Mode Selector */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>Cómo ingresar el stock:</Text>
            <View style={styles.modeSelector}>
              <TouchableOpacity
                style={[styles.modeButton, inputMode === 'packages' && styles.modeButtonActive]}
                onPress={() => setInputMode('packages')}
              >
                <Text style={[styles.modeButtonText, inputMode === 'packages' && styles.modeButtonTextActive]}>
                  Por Envases
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modeButton, inputMode === 'total' && styles.modeButtonActive]}
                onPress={() => setInputMode('total')}
              >
                <Text style={[styles.modeButtonText, inputMode === 'total' && styles.modeButtonTextActive]}>
                  Cantidad Total
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Package-based input */}
          {inputMode === 'packages' ? (
            <>
              <View style={styles.rowContainer}>
                <View style={[styles.formGroup, styles.rowItem]}>
                  <Text style={styles.label}>Cantidad de Envases *</Text>
                  <TextInput
                    style={[styles.input, errors.packageCount && styles.inputError]}
                    value={packageCount}
                    onChangeText={setPackageCount}
                    placeholder="Ej: 12"
                    keyboardType="numeric"
                  />
                  {errors.packageCount && <Text style={styles.errorText}>{errors.packageCount}</Text>}
                </View>

                <View style={[styles.formGroup, styles.rowItem]}>
                  <Text style={styles.label}>Tamaño por Envase *</Text>
                  <View style={styles.inputWithUnit}>
                    <TextInput
                      style={[styles.input, styles.inputWithUnitField, errors.packageSize && styles.inputError]}
                      value={packageSize}
                      onChangeText={setPackageSize}
                      placeholder="Ej: 60"
                      keyboardType="numeric"
                    />
                    <Text style={styles.unitText}>{unit}</Text>
                  </View>
                  {errors.packageSize && <Text style={styles.errorText}>{errors.packageSize}</Text>}
                </View>
              </View>
              
              {/* Total calculation display */}
              {packageCount && packageSize && (
                <View style={styles.calculationDisplay}>
                  <Text style={styles.calculationText}>
                    Stock Total: {packageCount} × {packageSize} {unit} = 
                    <Text style={styles.calculationResult}> {totalStock} {unit}</Text>
                  </Text>
                </View>
              )}
            </>
          ) : (
            /* Direct total input */
            <View style={styles.formGroup}>
              <Text style={styles.label}>Cantidad Total *</Text>
              <View style={styles.inputWithUnit}>
                <TextInput
                  style={[styles.input, styles.inputWithUnitField, errors.totalStock && styles.inputError]}
                  value={totalStock}
                  onChangeText={setTotalStock}
                  placeholder="Ej: 720"
                  keyboardType="numeric"
                />
                <Text style={styles.unitText}>{unit}</Text>
              </View>
              {errors.totalStock && <Text style={styles.errorText}>{errors.totalStock}</Text>}
            </View>
          )}

          <View style={styles.formGroup}>
            <Text style={styles.label}>Unidad de Medida</Text>
            <View style={styles.unitSelector}>
              <TouchableOpacity
                style={[styles.unitButton, unit === volumeUnit && styles.unitButtonActive]}
                onPress={() => setUnit(volumeUnit)}
              >
                <Text style={[styles.unitButtonText, unit === volumeUnit && styles.unitButtonTextActive]}>
                  {volumeUnit}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.unitButton, unit === weightUnit && styles.unitButtonActive]}
                onPress={() => setUnit(weightUnit)}
              >
                <Text style={[styles.unitButtonText, unit === weightUnit && styles.unitButtonTextActive]}>
                  {weightUnit}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.unitButton, unit === 'unidad' && styles.unitButtonActive]}
                onPress={() => setUnit('unidad')}
              >
                <Text style={[styles.unitButtonText, unit === 'unidad' && styles.unitButtonTextActive]}>
                  unidad
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Umbral Bajo Stock</Text>
            <View style={styles.inputWithUnit}>
              <TextInput
                style={[styles.input, styles.inputWithUnitField]}
                value={lowStockThreshold}
                onChangeText={setLowStockThreshold}
                placeholder="Ej: 120"
                keyboardType="numeric"
              />
              <Text style={styles.unitText}>{unit}</Text>
            </View>
            <Text style={styles.helperText}>
              Se te avisará cuando el stock baje de esta cantidad
            </Text>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Precio de Compra *</Text>
            <TextInput
              style={[styles.input, errors.purchasePrice && styles.inputError]}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              placeholder="Ej: 25.50"
              keyboardType="decimal-pad"
            />
            {errors.purchasePrice && <Text style={styles.errorText}>{errors.purchasePrice}</Text>}
            {purchasePrice && totalStock && (
              <Text style={styles.helperText}>
                Costo por {unit}: {formatCurrency(parseFloat(purchasePrice) / parseFloat(totalStock))}
              </Text>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Código Barras EAN (Opcional)</Text>
            <TextInput
              style={styles.input}
              value={barcode}
              onChangeText={setBarcode}
              placeholder="Ej: 8005610433172"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.cancelButton} 
              onPress={() => router.back()}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>
                {isLoading ? "Guardando..." : "Añadir Artículo"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  placeholder: {
    width: 34, // Same width as back button for centering
  },
  formContainer: {
    padding: spacing.md,
  },
  aiSection: {
    backgroundColor: Colors.light.card,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  aiHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  aiTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.sm,
    color: Colors.light.text,
  },
  aiInput: {
    marginBottom: spacing.sm,
  },
  aiButton: {
    backgroundColor: Colors.light.secondary,
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    alignItems: "center",
    ...shadows.sm,
  },
  aiButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    marginBottom: spacing.md,
    color: Colors.light.text,
  },
  formGroup: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.sm,
    color: Colors.light.text,
  },
  input: {
    backgroundColor: "#F5F5F7",
    borderWidth: 0,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm + 2,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  inputError: {
    borderColor: Colors.light.error,
    borderWidth: 1,
  },
  errorText: {
    color: Colors.light.error,
    fontSize: typography.sizes.xs,
    marginTop: spacing.xs,
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: spacing.sm,
  },
  rowItem: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.lg,
    gap: spacing.sm,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E5E5E5",
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    alignItems: "center",
  },
  cancelButtonText: {
    color: Colors.light.text,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  saveButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    alignItems: "center",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  saveButtonDisabled: {
    opacity: 0.7,
  },
  saveButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  pickerInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  pickerText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  pickerOptions: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E5E5E5",
    borderRadius: radius.md,
    marginTop: spacing.xs,
    overflow: "hidden",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  pickerOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm + 2,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  helperText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
  },
  modeSelector: {
    flexDirection: "row",
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  modeButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.md,
    backgroundColor: "#F5F5F7",
    alignItems: "center",
  },
  modeButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  modeButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.textSecondary,
  },
  modeButtonTextActive: {
    color: "white",
  },
  inputWithUnit: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.xs,
  },
  inputWithUnitField: {
    flex: 1,
  },
  unitText: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    minWidth: 30,
  },
  calculationDisplay: {
    backgroundColor: Colors.light.success + "10",
    padding: spacing.md,
    borderRadius: radius.sm,
    marginBottom: spacing.md,
  },
  calculationText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },
  calculationResult: {
    fontWeight: typography.weights.semibold,
    color: Colors.light.success,
  },
  unitSelector: {
    flexDirection: "row",
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  unitButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.sm,
    backgroundColor: "#F5F5F7",
  },
  unitButtonActive: {
    backgroundColor: Colors.light.primary,
  },
  unitButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  unitButtonTextActive: {
    color: "white",
    fontWeight: typography.weights.medium,
  },
});