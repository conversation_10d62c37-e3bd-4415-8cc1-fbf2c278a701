import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Platform } from "react-native";
import { useLocalSearchParams, Link, router } from "expo-router";
import { ChevronLeft, Phone, Mail, PlusCircle, ChevronRight, AlertTriangle, Shield, TrendingUp, Star } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useClientStore } from "@/stores/client-store";
import { useClientHistoryStore } from "@/stores/client-history-store";
import ClientHistoryPanel from "@/components/ClientHistoryPanel";

export default function ClientDetailScreen() {
  const { id } = useLocalSearchParams();
  const [client, setClient] = useState<any>(null);
  const [clientServices, setClientServices] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("services");

  const { getClient } = useClientStore();
  const {
    getClientProfile,
    getRecommendationsForClient,
    getWarningsForClient,
    initializeClientProfile
  } = useClientHistoryStore();

  useEffect(() => {
    // Find client by id
    const foundClient = getClient(id as string);
    if (foundClient) {
      setClient(foundClient);
      
      // Initialize client profile
      if (id) {
        initializeClientProfile(id as string);
        
        // Get services from client profile
        const profile = getClientProfile(id as string);
        if (profile && profile.previousFormulas) {
          // Convert previousFormulas to service format for display
          const services = profile.previousFormulas.map(formula => ({
            id: formula.id,
            clientId: id,
            clientName: foundClient.name,
            date: formula.date,
            type: "Coloración", // Default type since previousFormulas don't have this
            formula: formula.formula,
            satisfaction: formula.satisfaction,
            notes: formula.notes,
            aiAnalysis: {
              confidence: 90, // Default values since not stored
              processingTime: formula.processingTime ? `${formula.processingTime}s` : "N/A",
              recommendations: []
            }
          }));
          setClientServices(services);
        }
      }
    }
  }, [id, initializeClientProfile, getClientProfile]);

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' },
    default: {}
  });

  if (!client) {
    return (
      <View style={styles.container}>
        <Text>Cliente no encontrado</Text>
      </View>
    );
  }

  const clientProfile = id ? getClientProfile(id as string) : null;
  const warnings = id ? getWarningsForClient(id as string) : [];
  const recommendations = id ? getRecommendationsForClient(id as string) : [];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'alto': return Colors.light.error;
      case 'medio': return Colors.light.warning;
      default: return Colors.light.success;
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <View style={styles.clientAvatar}>
          <Text style={styles.clientInitial}>{client.name.charAt(0)}</Text>
        </View>
        <Text style={styles.clientName}>{client.name}</Text>
        <Text style={styles.clientSince}>Cliente desde {client.since}</Text>
        
        {/* Risk Level Indicator */}
        {clientProfile && (
          <View style={[styles.riskBadge, { backgroundColor: getRiskColor(clientProfile.riskLevel) + "20" }]}>
            <Shield size={14} color={getRiskColor(clientProfile.riskLevel)} />
            <Text style={[styles.riskText, { color: getRiskColor(clientProfile.riskLevel) }]}>
              RIESGO {clientProfile.riskLevel.toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.infoCard}>
        <View style={styles.infoItem}>
          <Mail size={18} color={Colors.light.primary} style={styles.infoIcon} />
          <Text style={styles.infoText}>{client.email}</Text>
        </View>
        <View style={styles.infoItem}>
          <Phone size={18} color={Colors.light.primary} style={styles.infoIcon} />
          <Text style={styles.infoText}>{client.phone}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoText}>Última visita: {client.lastVisit}</Text>
        </View>
        
        {/* Quick Stats */}
        {clientProfile && (
          <View style={styles.quickStats}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{clientProfile.totalServices}</Text>
              <Text style={styles.statLabel}>Servicios</Text>
            </View>
            <View style={styles.statItem}>
              <View style={styles.statRow}>
                <Star size={14} color={Colors.light.warning} />
                <Text style={styles.statNumber}>{clientProfile.averageSatisfaction.toFixed(1)}</Text>
              </View>
              <Text style={styles.statLabel}>Satisfacción</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{warnings.length}</Text>
              <Text style={styles.statLabel}>Alertas</Text>
            </View>
          </View>
        )}
      </View>

      {/* Warnings Banner */}
      {warnings.length > 0 && (
        <View style={styles.warningsBanner}>
          <AlertTriangle size={20} color={Colors.light.error} />
          <View style={styles.warningsContent}>
            <Text style={styles.warningsTitle}>Alertas Activas ({warnings.length})</Text>
            <Text style={styles.warningsText} numberOfLines={2}>
              {warnings.slice(0, 2).join(' • ')}
            </Text>
          </View>
        </View>
      )}

      {/* Quick Recommendations */}
      {recommendations.length > 0 && (
        <View style={styles.recommendationsBanner}>
          <TrendingUp size={20} color={Colors.light.accent} />
          <View style={styles.recommendationsContent}>
            <Text style={styles.recommendationsTitle}>Recomendaciones IA</Text>
            <Text style={styles.recommendationsText} numberOfLines={2}>
              {recommendations[0]}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.actionButtons}>
        <Link href={`/service/new?clientId=${client.id}`} style={linkStyle}>
          <View style={styles.primaryButton}>
            <Text style={styles.primaryButtonText}>Nuevo Análisis IA</Text>
          </View>
        </Link>
        <Link href={`/client/edit/${client.id}`} style={linkStyle}>
          <View style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>Editar Cliente</Text>
          </View>
        </Link>
      </View>

      <View style={styles.tabsContainer}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === "services" && styles.activeTab]}
          onPress={() => setActiveTab("services")}
        >
          <Text style={[styles.tabText, activeTab === "services" && styles.activeTabText]}>
            Historial de Servicios
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === "analysis" && styles.activeTab]}
          onPress={() => setActiveTab("analysis")}
        >
          <Text style={[styles.tabText, activeTab === "analysis" && styles.activeTabText]}>
            Perfil Completo
          </Text>
          {warnings.length > 0 && (
            <View style={styles.tabBadge}>
              <Text style={styles.tabBadgeText}>{warnings.length}</Text>
            </View>
          )}
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === "gallery" && styles.activeTab]}
          onPress={() => setActiveTab("gallery")}
        >
          <Text style={[styles.tabText, activeTab === "gallery" && styles.activeTabText]}>
            Galería
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === "services" && (
        <View style={styles.tabContent}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Servicios Realizados</Text>
            <Link href={`/service/new?clientId=${client.id}`} style={linkStyle}>
              <View>
                <PlusCircle size={20} color={Colors.light.primary} />
              </View>
            </Link>
          </View>
          
          {clientServices.length > 0 ? (
            clientServices.map((service) => (
              <Link key={service.id} href={`/service/${service.id}`} style={linkStyle}>
                <View style={styles.serviceItem}>
                  <View style={styles.serviceImageContainer}>
                    <Image source={{ uri: service.resultImageUrl }} style={styles.serviceImage} />
                  </View>
                  <View style={styles.serviceInfo}>
                    <Text style={styles.serviceDate}>{service.date}</Text>
                    <Text style={styles.serviceType}>{service.type}</Text>
                    <Text style={styles.serviceFormula} numberOfLines={1}>{service.formula}</Text>
                    <View style={styles.serviceSatisfaction}>
                      <Star size={12} color={Colors.light.warning} />
                      <Text style={styles.serviceSatisfactionText}>{service.satisfaction}/5</Text>
                    </View>
                  </View>
                  <ChevronRight size={16} color={Colors.light.gray} />
                </View>
              </Link>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No hay servicios registrados</Text>
              <Link href={`/service/new?clientId=${client.id}`} style={linkStyle}>
                <View style={styles.emptyStateButton}>
                  <Text style={styles.emptyStateButtonText}>Nuevo Análisis IA</Text>
                </View>
              </Link>
            </View>
          )}
        </View>
      )}

      {activeTab === "analysis" && (
        <View style={styles.tabContent}>
          {id && <ClientHistoryPanel clientId={id as string} />}
        </View>
      )}

      {activeTab === "gallery" && (
        <View style={styles.tabContent}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Galería de Inspiración</Text>
            <TouchableOpacity>
              <PlusCircle size={20} color={Colors.light.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No hay imágenes en la galería</Text>
            <TouchableOpacity style={styles.emptyStateButton}>
              <Text style={styles.emptyStateButtonText}>Añadir Imagen</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    backgroundColor: Colors.light.primary,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: spacing.xl,
    alignItems: "center",
  },
  backButton: {
    position: "absolute",
    top: Platform.OS === 'ios' ? 60 : 40,
    left: spacing.md,
    padding: 8,
  },
  clientAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.md,
    marginTop: spacing.lg,
  },
  clientInitial: {
    fontSize: 40,
    fontWeight: "700",
    color: "white",
  },
  clientName: {
    fontSize: 24,
    fontWeight: "700",
    color: "white",
    marginBottom: spacing.xs,
  },
  clientSince: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.9)",
    marginBottom: spacing.md,
  },
  riskBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: radius.lg,
    gap: spacing.xs,
  },
  riskText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold,
    textTransform: "uppercase",
  },
  infoCard: {
    backgroundColor: "white",
    borderRadius: radius.lg,
    padding: spacing.lg,
    margin: spacing.md,
    marginTop: -spacing.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoIcon: {
    marginRight: spacing.sm,
  },
  infoText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  quickStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  statItem: {
    alignItems: "center",
  },
  statRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.xs,
  },
  statNumber: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.gray,
    marginTop: spacing.xs,
  },
  warningsBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.error + "15",
    borderRadius: radius.md,
    padding: spacing.md,
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.error,
  },
  warningsContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  warningsTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.error,
    marginBottom: spacing.xs,
  },
  warningsText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.error,
    lineHeight: 16,
  },
  recommendationsBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.accent + "15",
    borderRadius: radius.md,
    padding: spacing.md,
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.accent,
  },
  recommendationsContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  recommendationsTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.accent,
    marginBottom: spacing.xs,
  },
  recommendationsText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.accent,
    lineHeight: 16,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    flex: 1,
    alignItems: "center",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  primaryButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  secondaryButton: {
    backgroundColor: "white",
    borderRadius: radius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    flex: 1,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E5E5E5",
  },
  secondaryButtonText: {
    color: Colors.light.text,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  tabsContainer: {
    flexDirection: "row",
    backgroundColor: "white",
    marginHorizontal: spacing.md,
    borderRadius: radius.lg,
    marginBottom: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: spacing.xs,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.primary,
  },
  tabText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  tabBadge: {
    backgroundColor: Colors.light.secondary,
    borderRadius: radius.sm,
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  tabBadgeText: {
    color: Colors.light.textLight,
    fontSize: 10,
    fontWeight: typography.weights.semibold,
  },
  tabContent: {
    backgroundColor: "white",
    borderRadius: radius.lg,
    padding: spacing.md,
    marginHorizontal: spacing.md,
    marginBottom: spacing.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  serviceItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  serviceImageContainer: {
    width: 50,
    height: 50,
    borderRadius: radius.sm,
    overflow: "hidden",
    marginRight: spacing.md,
  },
  serviceImage: {
    width: "100%",
    height: "100%",
  },
  serviceInfo: {
    flex: 1,
  },
  serviceDate: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    marginBottom: 2,
    color: Colors.light.text,
  },
  serviceType: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    marginBottom: 2,
  },
  serviceFormula: {
    fontSize: typography.sizes.xs,
    color: Colors.light.gray,
    marginBottom: spacing.xs,
  },
  serviceSatisfaction: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.xs,
  },
  serviceSatisfactionText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.warning,
    fontWeight: typography.weights.medium,
  },
  emptyState: {
    alignItems: "center",
    padding: spacing.lg,
  },
  emptyStateText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginBottom: spacing.md,
  },
  emptyStateButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.full,
    ...shadows.sm,
  },
  emptyStateButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.sm,
  },
});