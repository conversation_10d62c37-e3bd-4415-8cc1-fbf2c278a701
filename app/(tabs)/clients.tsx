import React, { useState, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, TextInput, FlatList, Platform, Alert } from "react-native";
import { Link } from "expo-router";
import { Search, Plus, Eye, Edit, Trash2, AlertTriangle, Shield, TrendingUp, Phone, Calendar } from "lucide-react-native";
import Colors from "@/constants/colors";
import { typography, spacing, radius, shadows } from "@/constants/theme";
import { BaseCard, BaseButton } from "@/components/base";
import { useClientStore } from "@/stores/client-store";
import { useClientHistoryStore } from "@/stores/client-history-store";
import { usePermissions } from "@/hooks/usePermissions";

export default function ClientsScreen() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterMode, setFilterMode] = useState<'all' | 'warnings' | 'recommendations'>('all');

  const { clients, deleteClient: deleteClientFromStore } = useClientStore();
  const { 
    getWarningsForClient, 
    getRecommendationsForClient, 
    getClientProfile,
    initializeClientProfile 
  } = useClientHistoryStore();
  const { can } = usePermissions();

  // Initialize client profiles on mount
  useEffect(() => {
    clients.forEach(client => {
      initializeClientProfile(client.id);
    });
  }, [clients, initializeClientProfile]);

  const getFilteredClients = () => {
    let filtered = clients.filter(
      (client) =>
        client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.email.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (filterMode === 'warnings') {
      filtered = filtered.filter(client => getWarningsForClient(client.id).length > 0);
    } else if (filterMode === 'recommendations') {
      filtered = filtered.filter(client => getRecommendationsForClient(client.id).length > 0);
    }

    return filtered;
  };

  const filteredClients = getFilteredClients();

  const deleteClient = (id: string) => {
    Alert.alert(
      "Eliminar Cliente",
      "¿Estás seguro de que quieres eliminar este cliente? Esta acción no se puede deshacer.",
      [
        { text: "Cancelar", style: "cancel" },
        { 
          text: "Eliminar", 
          style: "destructive",
          onPress: () => deleteClientFromStore(id)
        }
      ]
    );
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'alto': return Colors.light.error;
      case 'medio': return Colors.light.warning;
      default: return Colors.light.success;
    }
  };

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' },
    default: {}
  });

  const renderClientCard = ({ item }: { item: any }) => {
    const warnings = getWarningsForClient(item.id);
    const recommendations = getRecommendationsForClient(item.id);
    const profile = getClientProfile(item.id);

    return (
      <View style={styles.clientCard}>
        <View style={styles.clientAvatar}>
          <Text style={styles.clientInitial}>{item.name.charAt(0)}</Text>
          {warnings.length > 0 && (
            <View style={styles.warningBadge}>
              <Text style={styles.warningBadgeText}>{warnings.length}</Text>
            </View>
          )}
        </View>
        
        <View style={styles.clientInfo}>
          <View style={styles.clientHeader}>
            <Text style={styles.clientName}>{item.name}</Text>
            {profile && (
              <View style={[
                styles.riskBadge,
                { backgroundColor: getRiskColor(profile.riskLevel) + "20" }
              ]}>
                <Text style={[
                  styles.riskText,
                  { color: getRiskColor(profile.riskLevel) }
                ]}>
                  {profile.riskLevel.toUpperCase()}
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.clientDetails}>
            <View style={styles.detailRow}>
              <Phone size={14} color={Colors.light.gray} />
              <Text style={styles.clientPhone}>{item.phone}</Text>
            </View>
            <View style={styles.detailRow}>
              <Calendar size={14} color={Colors.light.gray} />
              <Text style={styles.clientLastVisit}>Última visita: {item.lastVisit || '10 Mayo 2023'}</Text>
            </View>
          </View>
          
          {warnings.length > 0 && (
            <View style={styles.warningInfo}>
              <AlertTriangle size={12} color={Colors.light.error} />
              <Text style={styles.warningText} numberOfLines={1}>
                {warnings[0]}
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.actionsContainer}>
          <Link href={`/client/${item.id}`} style={linkStyle}>
            <View style={[styles.actionButton, styles.viewButton]}>
              <Eye size={16} color={Colors.light.primary} />
            </View>
          </Link>
          <Link href={`/client/edit/${item.id}`} style={linkStyle}>
            <View style={[styles.actionButton, styles.editButton]}>
              <Edit size={16} color={Colors.light.accent} />
            </View>
          </Link>
          {can.deleteData && (
            <TouchableOpacity 
              style={[styles.actionButton, styles.deleteButton]} 
              onPress={() => deleteClient(item.id)}
            >
              <Trash2 size={16} color={Colors.light.error} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar por nombre o email..."
            placeholderTextColor={Colors.light.gray}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          <Link href="/client/new" style={linkStyle}>
            <View style={styles.addButton}>
              <Plus size={20} color={Colors.light.primary} />
            </View>
          </Link>
        </View>

        <View style={styles.filtersContainer}>
          <TouchableOpacity
            style={[styles.filterButton, filterMode === 'all' && styles.activeFilterButton]}
            onPress={() => setFilterMode('all')}
          >
            <Text style={[styles.filterText, filterMode === 'all' && styles.activeFilterText]}>
              Todos ({clients.length})
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.filterButton, filterMode === 'warnings' && styles.activeFilterButton]}
            onPress={() => setFilterMode('warnings')}
          >
            <AlertTriangle size={14} color={filterMode === 'warnings' ? "white" : Colors.light.error} />
            <Text style={[styles.filterText, filterMode === 'warnings' && styles.activeFilterText]}>
              Con Alertas ({clients.filter(c => getWarningsForClient(c.id).length > 0).length})
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.filterButton, filterMode === 'recommendations' && styles.activeFilterButton]}
            onPress={() => setFilterMode('recommendations')}
          >
            <TrendingUp size={14} color={filterMode === 'recommendations' ? "white" : Colors.light.accent} />
            <Text style={[styles.filterText, filterMode === 'recommendations' && styles.activeFilterText]}>
              Con Recomendaciones
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            {filteredClients.length} cliente{filteredClients.length !== 1 ? 's' : ''} encontrado{filteredClients.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <FlatList
          data={filteredClients}
          keyExtractor={(item) => item.id}
          renderItem={renderClientCard}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Shield size={48} color={Colors.light.gray} />
              <Text style={styles.emptyStateTitle}>
                {filterMode === 'all' ? 'No se encontraron clientes' :
                 filterMode === 'warnings' ? 'No hay clientes con alertas' :
                 'No hay clientes con recomendaciones'}
              </Text>
              <Text style={styles.emptyStateText}>
                {filterMode === 'all' ? 'Comienza añadiendo tu primer cliente' :
                 filterMode === 'warnings' ? 'Todos tus clientes están sin alertas activas' :
                 'No hay recomendaciones pendientes'}
              </Text>
              {filterMode === 'all' && (
                <Link href="/client/new" style={linkStyle}>
                  <View style={styles.emptyStateButton}>
                    <Plus size={20} color="white" style={styles.emptyStateButtonIcon} />
                    <Text style={styles.emptyStateButtonText}>Añadir Cliente</Text>
                  </View>
                </Link>
              )}
            </View>
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F7",
  },
  content: {
    flex: 1,
    padding: 20,
    paddingTop: 10,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
  },
  filtersContainer: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 8,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: radius.full,
    backgroundColor: "white",
    gap: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  activeFilterButton: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  filterText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: "500",
  },
  activeFilterText: {
    color: "white",
  },
  statsContainer: {
    marginBottom: 16,
  },
  statsText: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: "600",
  },
  listContainer: {
    flexGrow: 1,
  },
  clientCard: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  clientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary + "15",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    position: "relative",
  },
  clientInitial: {
    fontSize: 20,
    fontWeight: "700",
    color: Colors.light.primary,
  },
  warningBadge: {
    position: "absolute",
    top: -4,
    right: -4,
    backgroundColor: Colors.light.error,
    borderRadius: radius.full,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  warningBadgeText: {
    color: "white",
    fontSize: 11,
    fontWeight: "600",
  },
  clientInfo: {
    flex: 1,
  },
  clientHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  clientName: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.light.text,
    flex: 1,
  },
  riskBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 6,
  },
  riskText: {
    fontSize: 10,
    fontWeight: "600",
  },
  clientDetails: {
    gap: 4,
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  clientPhone: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  clientLastVisit: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  warningInfo: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.error + "10",
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  warningText: {
    fontSize: 11,
    color: Colors.light.error,
    fontWeight: "500",
    flex: 1,
  },
  actionsContainer: {
    flexDirection: "column",
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.light.primary + "10",
  },
  viewButton: {
    backgroundColor: Colors.light.primary + "10",
  },
  editButton: {
    backgroundColor: Colors.light.accent + "10",
  },
  deleteButton: {
    backgroundColor: Colors.light.error + "10",
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: Colors.light.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: "center",
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 32,
    textAlign: "center",
    lineHeight: 22,
  },
  emptyStateButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    gap: 8,
  },
  emptyStateButtonIcon: {
  },
  emptyStateButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});