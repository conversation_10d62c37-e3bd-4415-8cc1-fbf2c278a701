# Claude Code - Guía de Trabajo para Salonier

**Última actualización**: 2025-01-14  
**Versión**: v2.0.5  
**Estado**: Desarrollo estable  

---

## 📍 Protocolo de Inicio (OBLIGATORIO)

Al comenzar CADA nueva sesión de Claude Code:

1. **Leer `planning.md`** - Visión, arquitectura y estado actual
2. **Revisar `todo.md`** - Lista de tareas por hitos
3. **Verificar `PRD.md`** - Si necesitas contexto del producto

### Durante el trabajo:
- 📝 **PRIMERO**: Escribir plan detallado antes de ejecutar
- ✅ Marcar tareas completadas en `todo.md` inmediatamente
- ➕ Añadir nuevas tareas descubiertas
- 🔄 Mantener documentación sincronizada
- 📊 Documentar cada cambio paso a paso
- 🔒 Revisar seguridad en cada modificación

---

## 📋 Protocolo de Desarrollo Estructurado

### Flujo de trabajo para cada tarea:

1. **Ana<PERSON><PERSON> el problema**
   - Leer el código base para encontrar los archivos relevantes
   - Escribir un plan detallado en todo.md

2. **Crear plan de acción**
   - El plan debe incluir una lista de tareas pendientes
   - Cada tarea debe ser simple y atómica
   - Mínimo impacto en el código existente

3. **Verificación con usuario**
   - Presentar el plan antes de ejecutar
   - Esperar confirmación explícita

4. **Ejecución paso a paso**
   - Trabajar en las tareas pendientes
   - Marcar cada tarea como completada en todo.md
   - Explicar detalladamente cada cambio

5. **Revisión de seguridad**
   - Verificar que no hay vulnerabilidades
   - Asegurar que no hay información confidencial
   - Validar mejores prácticas de seguridad

6. **Documentación educativa**
   - Explicar funcionalidad y cambios en detalle
   - Actuar como ingeniero senior enseñando
   - Incluir el "por qué" de cada decisión

7. **Sección de revisión**
   - Agregar resumen de cambios en todo.md
   - Documentar lecciones aprendidas
   - Identificar próximos pasos

### Principio fundamental: SIMPLICIDAD
- Cada cambio debe ser el más mínimo posible
- Evitar refactorizaciones masivas
- Preferir múltiples cambios pequeños a uno grande
- Si un cambio afecta > 50 líneas, considerar dividirlo

---

## 🚀 Comandos de Inicio Rápido

```bash
# Al iniciar cada sesión
cat planning.md      # ¿Cuál es el estado del proyecto?
cat todo.md         # ¿Qué tareas están pendientes?
git status          # ¿Hay cambios sin commitear?
npm test           # ¿Todo funciona?
```

---

## 💡 Principios Clave del Proyecto

### 100% IA Generativa
- **NO usar** algoritmos tradicionales, tablas o fórmulas predefinidas
- **SIEMPRE** generar fórmulas únicas con GPT-4o
- Cada fórmula debe ser razonada contextualmente

### Arquitectura Técnica
- **Offline-first**: Toda operación funciona sin conexión
- **UI Optimistic**: Actualizaciones instantáneas, sync en background
- **Multi-tenant**: RLS garantiza aislamiento total por salón

### Mejores Prácticas
- Usar TypeScript estricto
- Seguir patrones existentes en el código
- Documentar decisiones importantes
- Probar cambios críticos

---

## 📁 Estructura de Documentación

```
Raíz/
├── planning.md    # Visión y arquitectura
├── todo.md       # Tareas activas por hitos
├── PRD.md        # Requerimientos del producto
├── README.md     # Doc principal
├── CHANGELOG.md  # Historial de versiones
└── docs/         # Documentación técnica específica
```

---

## 🛠 Herramientas y Configuración

### Desarrollo Local
```bash
npm run ios       # iOS Simulator
npm run android   # Android Emulator
npm run web       # Web (limitado)
```

### Testing
```bash
npm test          # Unit tests
npm run lint      # Linting
npm run type-check # TypeScript
```

### Supabase
- Project: ajsamgugqfbttkrlgvbr
- Edge Function: salonier-assistant (v9)
- Ver `docs/README_SETUP.md` para configuración

---

## ⚠️ Reglas Importantes

1. **NO crear** archivos .md innecesarios
2. **NO duplicar** información ya documentada
3. **SIEMPRE actualizar** todo.md al completar tareas
4. **SEGUIR** la arquitectura establecida
5. **CONSULTAR** PRD.md para decisiones de producto

---

## 🔒 Protocolo de Seguridad

### Checklist obligatorio para cada cambio:

1. **Información sensible**
   - [ ] No hay API keys hardcodeadas
   - [ ] No hay contraseñas en texto plano
   - [ ] No hay datos personales expuestos
   - [ ] Logs no revelan información crítica

2. **Vulnerabilidades comunes**
   - [ ] Validación de inputs del usuario
   - [ ] Protección contra SQL injection
   - [ ] Sanitización de datos para UI
   - [ ] Manejo seguro de errores

3. **Mejores prácticas**
   - [ ] Uso de HTTPS para todas las llamadas
   - [ ] Tokens con expiración adecuada
   - [ ] Principio de menor privilegio
   - [ ] Encriptación de datos sensibles

---

## 📚 Protocolo de Documentación Educativa

### Para cada cambio significativo:

1. **Explicar el contexto**
   - ¿Qué problema resuelve?
   - ¿Por qué esta solución?
   - ¿Qué alternativas se consideraron?

2. **Detallar la implementación**
   - Flujo de datos paso a paso
   - Patrones de diseño utilizados
   - Decisiones de arquitectura

3. **Enseñar conceptos**
   - Explicar como si fuera la primera vez
   - Incluir analogías cuando sea útil
   - Proporcionar recursos adicionales

4. **Código comentado**
   - Comentarios del "por qué", no del "qué"
   - Ejemplos de uso cuando sea relevante
   - Advertencias sobre casos edge

---

## 🐛 Debugging

### Problemas Comunes
- **Timeout IA**: Ya implementado con 30s + retry
- **Sincronización**: Verificar SyncIndicator
- **Edge Function**: Logs pueden tardar 10-30s

### Comandos Útiles
```bash
# Limpiar cache
npm start --clear

# Regenerar tipos Supabase
npx supabase gen types typescript --project-id ajsamgugqfbttkrlgvbr

# Ver logs Edge Function
supabase functions logs salonier-assistant --tail
```

---

## 📝 Historial de Sesiones Relevantes

### 2025-01-13 (Sesión 2): Sistema Contextual por Técnica

**Objetivo**: Hacer que la IA genere fórmulas específicas según la técnica de aplicación seleccionada.

**Trabajo realizado**:
1. ✅ **Mejora de Edge Function**
   - Añadidos prompts específicos para las 10 técnicas disponibles
   - Cada técnica tiene consideraciones únicas (volumen oxidante, consistencia, tiempos)
   - Soporte bilingüe (español/inglés) mantenido

2. ✅ **Eliminación de lógica hardcodeada**
   - Removidas fórmulas predefinidas para balayage, mechas y tinte completo
   - Ahora la IA genera fórmulas contextuales basadas en la técnica seleccionada
   - Mantenido solo un fallback genérico si la IA falla

3. ✅ **Prompts mejorados por técnica**
   - Balayage: consistencia cremosa, oxidante bajo (20 vol max)
   - Mechas: consistencia espesa, papel aluminio, hasta 30 vol
   - Babylights: secciones ultrafinas, 10-20 vol
   - Corrección color: análisis de pigmentos, múltiples pasos
   - Y 6 técnicas más con instrucciones específicas

**Resultado**: La IA ahora genera fórmulas verdaderamente contextuales según la técnica seleccionada, mejorando significativamente la precisión y utilidad del sistema.

### 2025-01-13 (Sesión 1): Reorganización Completa de Documentación

**Objetivo**: Limpiar y reorganizar toda la documentación del proyecto.

**Trabajo realizado**:
1. ✅ **Actualización de PRD.md**
   - Corregido: Sistema 100% IA generativa (no algoritmos)
   - Actualizado a v2.0 con estado actual del proyecto
   - Añadidas métricas reales de producción

2. ✅ **Creación de planning.md**
   - Documento maestro con visión y arquitectura
   - Stack tecnológico completo
   - Estado actual y roadmap
   - Guía de inicio rápido

3. ✅ **Reorganización de todo.md**
   - Estructura por 8 hitos principales
   - Todas las tareas organizadas en viñetas
   - Estado claro: completado/en progreso/pendiente
   - Eliminación de secciones redundantes

4. ✅ **Limpieza de archivos**
   - 13 archivos .md redundantes eliminados
   - Documentación técnica movida a /docs
   - Estructura final: 6 archivos en raíz + carpeta docs

**Resultado**: Documentación clara, sin redundancias, con todo.md como lista maestra de tareas por hitos.

### 2025-01-14 (Sesión 1): Optimización Mayor de Performance

**Objetivo**: Optimizar el sistema de análisis de IA para reducir latencia y mejorar mantenibilidad.

**Trabajo realizado**:
1. ✅ **Refactorización de ai-analysis-store.ts**
   - Reducción de 646 a 365 líneas (-43%)
   - Función `performImageAnalysis` unificada elimina ~200 líneas duplicadas
   - Retry logic mejorado con exponential backoff (3 reintentos máx)
   - Validación de respuestas más robusta

2. ✅ **Sistema de Logging Condicional**
   - Creado `utils/logger.ts` - logs solo en desarrollo
   - Errores siempre visibles (críticos para producción)
   - Sin impacto de performance en producción

3. ✅ **ImageProcessor Centralizado**
   - Creado `utils/image-processor.ts` (296 líneas)
   - Compresión inteligente con cache de 5 minutos
   - Validación de calidad de imágenes
   - Perfiles de compresión por propósito (diagnosis/desired)
   - Generación de hashes para deduplicación

**Mejoras logradas**:
- ⚡ Reducción esperada de latencia IA: -30%
- 📦 Reducción de código duplicado: -281 líneas
- 🚀 Cache evita recompresiones innecesarias
- 🔧 Código más mantenible y modular

### 2025-01-14 (Sesión 2): Phase 2 - Optimización de Stores

**Objetivo**: Continuar con Phase 2 de optimizaciones para preparar el código para producción.

**Trabajo realizado**:
1. ✅ **FASE 1 - Logger Utility**
   - Extendido `utils/logger.ts` con nuevas funcionalidades
   - Soporte para performance timing (startTimer/endTimer)
   - Logging contextual con withContext()
   - Formateo consistente con timestamps

2. ✅ **FASE 2 - Auth Store**
   - Aplicado logger a 18 console.* statements
   - Extraído `loadPreferencesFromSupabase()` (elimina duplicación)
   - Extraído `syncAllStores()` para lógica de sincronización
   - Refactorizado `signUp()` con `waitForProfileCreation()` y `attemptManualProfileSetup()`
   - Resultado: 537 → 543 líneas (ligero aumento por JSDoc, pero mejor organización)

3. ✅ **FASE 3 - Inventory Store**
   - Reducción masiva: 1,157 → 877 líneas (-24.2%)
   - Creado `data/default-products.ts` (224 líneas extraídas)
   - Implementado `handleSyncError()` para manejo consistente
   - Refactorizado `generateInventoryReport()` en 3 funciones:
     - `calculateStockMetrics()`
     - `calculateMostUsedProducts()`
     - `calculateCostByCategory()`
   - Logger aplicado en todas las operaciones

4. ✅ **FASE 4 - Client History Store** 
   - Reducción: 882 → 781 líneas (-11.5%)
   - Creado `syncRecord()` genérico para operaciones de sincronización
   - Refactorizado `getWarningsForClient()` en 3 métodos específicos:
     - `checkAllergies()`
     - `checkPatchTests()`
     - `checkConsent()`
   - Logger aplicado con métricas de performance

5. ✅ **FASE 5 - Cleanup Final**
   - Eliminados archivos obsoletos:
     - `client-history-store-old.ts`
     - `inventory-store-old.ts`
   - Total: 1,280 líneas removidas

**Resultados Phase 2**:
- 📦 Total líneas removidas: ~1,660 líneas
- 📊 Reducción promedio: ~25% en stores optimizados
- 🚀 Sistema de logging consistente implementado
- 🔧 Mejor organización con funciones helper extraídas
- ✅ 100% funcionalidad mantenida

**Próximos pasos recomendados**:
- Aplicar logger a stores restantes
- Optimizar componentes grandes con lazy loading
- Implementar paginación en listas largas

---

*Para contexto completo del proyecto, siempre consultar planning.md y todo.md*

---

## 📄 Template de Plan de Trabajo

Cuando crees un plan en todo.md, usa este formato:

```markdown
## 🎯 Plan de Trabajo [Fecha]

### Análisis del Problema
- **Problema identificado**: [descripción clara]
- **Archivos afectados**: 
  - [ ] archivo1.ts
  - [ ] archivo2.tsx
- **Impacto estimado**: [líneas/componentes]
- **Riesgos identificados**: [si los hay]

### Tareas a Realizar
- [ ] Tarea 1: [descripción simple y específica]
- [ ] Tarea 2: [descripción simple y específica]
- [ ] Tarea 3: [descripción simple y específica]

### Validaciones
- [ ] Tests pasan
- [ ] Linting sin errores
- [ ] TypeScript sin errores
- [ ] Revisión de seguridad completada

### Sección de Revisión
- **Cambios realizados**: [resumen]
- **Problemas encontrados**: [si los hubo]
- **Lecciones aprendidas**: [conocimiento adquirido]
- **Próximos pasos**: [qué sigue]
```