{
  "compilerOptions": {
    // ========== CONFIGURACIÓN BASE ==========
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "moduleResolution": "node",
    "allowJs": true,
    "checkJs": false,
    "jsx": "react-jsx",
    
    // ========== OPTIMIZACIONES DE RENDIMIENTO ==========
    
    // Compilación incremental para builds más rápidos
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo",
    
    // Skip lib check para builds más rápidos
    "skipLibCheck": true,
    
    // Usar project references para proyectos grandes
    "composite": false,
    
    // ========== TYPE CHECKING ESTRICTO ==========
    
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    
    // ========== DETECCIÓN DE CÓDIGO NO UTILIZADO ==========
    
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    
    // ========== RESOLUCIÓN DE MÓDULOS ==========
    
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    
    // ========== PATHS PARA IMPORTS ABSOLUTOS ==========
    
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/stores/*": ["./stores/*"],
      "@/services/*": ["./services/*"],
      "@/utils/*": ["./utils/*"],
      "@/types/*": ["./types/*"],
      "@/constants/*": ["./constants/*"],
      "@/hooks/*": ["./hooks/*"],
      "@/lib/*": ["./lib/*"],
      "@/docs/*": ["./docs/*"]
    },
    
    // ========== GENERACIÓN DE ARCHIVOS ==========
    
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "outDir": "./dist",
    "removeComments": true,
    "noEmit": true,
    
    // ========== INTEROPERABILIDAD ==========
    
    "allowUmdGlobalAccess": true,
    
    // ========== CONFIGURACIÓN REACT NATIVE ==========
    
    "types": [
      "react-native",
      "jest",
      "@types/react",
      "@types/react-native"
    ]
  },
  
  // ========== ARCHIVOS A INCLUIR ==========
  
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.js",
    "**/*.jsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts"
  ],
  
  // ========== ARCHIVOS A EXCLUIR ==========
  
  "exclude": [
    "node_modules",
    ".expo",
    "dist",
    "build",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/__tests__/**/*",
    "metro.config.js",
    "babel.config.js",
    "jest.config.js"
  ],
  
  // ========== CONFIGURACIÓN ESPECÍFICA PARA OPTIMIZACIÓN ==========
  
  "ts-node": {
    "esm": true,
    "compilerOptions": {
      "module": "ESNext",
      "target": "ES2020"
    }
  },
  
  // ========== CONFIGURACIÓN PARA HERRAMIENTAS ==========
  
  "compileOnSave": false,
  
  // ========== CONFIGURACIÓN EXPERIMENTAL ==========
  
  "experimentalDecorators": false,
  "emitDecoratorMetadata": false,
  
  // ========== CONFIGURACIÓN DE WATCH MODE ==========
  
  "watchOptions": {
    "watchFile": "useFsEvents",
    "watchDirectory": "useFsEvents",
    "fallbackPolling": "dynamicPriority",
    "synchronousWatchDirectory": true,
    "excludeDirectories": [
      "**/node_modules",
      "**/.expo",
      "**/dist",
      "**/build"
    ]
  }
}
