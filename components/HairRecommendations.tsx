import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Lightbulb, AlertTriangle, Shield } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { HairDiagnosis, generateRecommendations } from '@/types/hair-diagnosis';

interface HairRecommendationsProps {
  diagnosis: Partial<HairDiagnosis>;
  onUpdate?: () => void;
}

export default function HairRecommendations({ diagnosis, onUpdate }: HairRecommendationsProps) {
  // Generate recommendations based on current diagnosis
  const recommendations = React.useMemo(() => {
    if (!diagnosis.thickness || !diagnosis.porosity || !diagnosis.density || 
        !diagnosis.elasticity || !diagnosis.resistance) {
      return null;
    }
    
    return generateRecommendations(diagnosis as HairDiagnosis);
  }, [diagnosis]);

  if (!recommendations) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Lightbulb size={24} color={Colors.light.gray} />
          <Text style={styles.emptyText}>
            Complete el diagnóstico para ver recomendaciones personalizadas
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Lightbulb size={20} color={Colors.light.accent} />
        <Text style={styles.title}>Recomendaciones Personalizadas</Text>
      </View>

      {/* Products Section */}
      {recommendations.products.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Productos Recomendados</Text>
          {recommendations.products.map((product, index) => (
            <View key={index} style={styles.item}>
              <Shield size={16} color={Colors.light.success} />
              <Text style={styles.itemText}>{product}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Treatments Section */}
      {recommendations.treatments.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tratamientos Sugeridos</Text>
          {recommendations.treatments.map((treatment, index) => (
            <View key={index} style={styles.item}>
              <Shield size={16} color={Colors.light.primary} />
              <Text style={styles.itemText}>{treatment}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Precautions Section */}
      {recommendations.precautions.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Precauciones</Text>
          {recommendations.precautions.map((precaution, index) => (
            <View key={index} style={styles.warningItem}>
              <AlertTriangle size={16} color={Colors.light.warning} />
              <Text style={styles.warningText}>{precaution}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Coloring Suggestions */}
      {recommendations.coloringSuggestions && recommendations.coloringSuggestions.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sugerencias de Coloración</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {recommendations.coloringSuggestions.map((suggestion, index) => (
              <View key={index} style={styles.colorCard}>
                <Text style={styles.colorCardTitle}>{suggestion.targetColor}</Text>
                <View style={styles.difficultyBadge}>
                  <Text style={styles.difficultyText}>
                    Dificultad: {suggestion.difficulty}
                  </Text>
                </View>
                <Text style={styles.sessionsText}>
                  {suggestion.estimatedSessions} {suggestion.estimatedSessions === 1 ? 'sesión' : 'sesiones'}
                </Text>
                <View style={styles.processesContainer}>
                  {suggestion.requiredProcesses.map((process, idx) => (
                    <Text key={idx} style={styles.processText}>• {process}</Text>
                  ))}
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.cardBackground,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: 'center',
    marginTop: 8,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingLeft: 8,
  },
  itemText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 8,
    flex: 1,
  },
  warningItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingLeft: 8,
    backgroundColor: Colors.light.warning + '10',
    borderRadius: 8,
    padding: 8,
  },
  warningText: {
    fontSize: 14,
    color: Colors.light.warning,
    marginLeft: 8,
    flex: 1,
    fontWeight: '500',
  },
  colorCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    padding: 12,
    marginRight: 12,
    width: 180,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  colorCardTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  difficultyBadge: {
    backgroundColor: Colors.light.primary + '20',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  difficultyText: {
    fontSize: 12,
    color: Colors.light.primary,
    fontWeight: '500',
  },
  sessionsText: {
    fontSize: 13,
    color: Colors.light.gray,
    marginBottom: 8,
  },
  processesContainer: {
    marginTop: 4,
  },
  processText: {
    fontSize: 12,
    color: Colors.light.text,
    marginBottom: 2,
  },
});