/**
 * OptimizedImage Component
 * 
 * Componente optimizado para carga de imágenes con:
 * - Lazy loading
 * - Placeholder mientras carga
 * - Manejo de errores
 * - Caching mejorado
 * - Fade-in animation
 */

import React, { useState, useEffect, memo, useRef } from 'react';
import {
  Image,
  ImageProps,
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  Animated,
  Platform,
  ImageURISource
} from 'react-native';
import { BlurView } from 'expo-blur';
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';
import Colors from '@/constants/colors';
import { logger } from '@/utils/logger';

// Configuración de cache
const CACHE_DIR = `${FileSystem.cacheDirectory}optimized-images/`;
const CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 días

// Asegurar que el directorio de cache existe
const ensureCacheDir = async () => {
  const dirInfo = await FileSystem.getInfoAsync(CACHE_DIR);
  if (!dirInfo.exists) {
    await FileSystem.makeDirectoryAsync(CACHE_DIR, { intermediates: true });
  }
};

// Inicializar directorio de cache
ensureCacheDir().catch(err => {
  logger.error('Failed to create image cache directory', err);
});

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  source: ImageURISource;
  placeholderColor?: string;
  showLoadingIndicator?: boolean;
  fadeDuration?: number;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';
  cacheEnabled?: boolean;
  lowQualitySource?: ImageURISource;
  priority?: 'low' | 'normal' | 'high';
}

// Componente principal
const OptimizedImage = ({
  source,
  placeholderColor = Colors.light.background,
  showLoadingIndicator = true,
  fadeDuration = 300,
  resizeMode = 'cover',
  cacheEnabled = true,
  lowQualitySource,
  priority = 'normal',
  style,
  onLoad,
  onError,
  ...props
}: OptimizedImageProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cachedUri, setCachedUri] = useState<string | null>(null);
  const opacity = useRef(new Animated.Value(0)).current;
  const [lowQualityLoaded, setLowQualityLoaded] = useState(false);

  // Prioridad de carga
  const loadPriority = {
    low: 0.5,
    normal: 0.75,
    high: 1.0
  }[priority];

  useEffect(() => {
    let isMounted = true;
    setIsLoading(true);
    setError(null);
    opacity.setValue(0);

    const loadImage = async () => {
      // Solo procesar URIs remotas
      if (!source.uri || source.uri.startsWith('data:') || source.uri.startsWith('file:')) {
        if (isMounted) {
          setCachedUri(source.uri || null);
          setIsLoading(false);
        }
        return;
      }

      if (cacheEnabled) {
        try {
          // Generar nombre de archivo para cache
          const filename = await Crypto.digestStringAsync(
            Crypto.CryptoDigestAlgorithm.SHA1,
            source.uri
          );
          const cacheFilePath = `${CACHE_DIR}${filename}.jpg`;

          // Verificar si ya está en cache
          const cacheInfo = await FileSystem.getInfoAsync(cacheFilePath);
          
          if (cacheInfo.exists) {
            // Verificar si el cache ha expirado
            if (cacheInfo.modificationTime && 
                Date.now() - cacheInfo.modificationTime * 1000 < CACHE_EXPIRY) {
              if (isMounted) {
                setCachedUri(cacheFilePath);
                setIsLoading(false);
              }
              return;
            }
          }

          // Descargar y guardar en cache
          if (isMounted) {
            const downloadResumable = FileSystem.createDownloadResumable(
              source.uri,
              cacheFilePath,
              {},
              (downloadProgress) => {
                const progress = downloadProgress.totalBytesWritten / 
                                 downloadProgress.totalBytesExpectedToWrite;
                // Podríamos usar esto para mostrar progreso
              }
            );

            const result = await downloadResumable.downloadAsync();
            if (result && result.uri && isMounted) {
              setCachedUri(result.uri);
              setIsLoading(false);
            }
          }
        } catch (err) {
          logger.error('Image caching error', err);
          // Fallback a carga normal si falla el cache
          if (isMounted) {
            setCachedUri(source.uri);
            setIsLoading(false);
          }
        }
      } else {
        // Sin cache, usar URI original
        if (isMounted) {
          setCachedUri(source.uri);
          setIsLoading(false);
        }
      }
    };

    // Retrasar carga según prioridad
    const timeout = priority === 'high' ? 0 : 100;
    const timer = setTimeout(loadImage, timeout);

    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, [source.uri, cacheEnabled, priority]);

  const handleLoad = () => {
    Animated.timing(opacity, {
      toValue: 1,
      duration: fadeDuration,
      useNativeDriver: true,
    }).start();

    if (onLoad) {
      onLoad({ nativeEvent: { source: { width: 0, height: 0 } } } as any);
    }
  };

  const handleError = (e: any) => {
    setError('Error al cargar la imagen');
    setIsLoading(false);
    if (onError) onError(e);
  };

  const handleLowQualityLoad = () => {
    setLowQualityLoaded(true);
  };

  return (
    <View style={[styles.container, style]}>
      {/* Placeholder de color */}
      <View 
        style={[
          StyleSheet.absoluteFill, 
          { backgroundColor: placeholderColor }
        ]} 
      />
      
      {/* Imagen de baja calidad (si se proporciona) */}
      {lowQualitySource && !lowQualityLoaded && (
        <Image
          source={lowQualitySource}
          style={StyleSheet.absoluteFill}
          resizeMode={resizeMode}
          onLoad={handleLowQualityLoad}
          blurRadius={Platform.OS === 'ios' ? 10 : 5}
        />
      )}

      {/* Imagen principal */}
      {cachedUri && (
        <Animated.Image
          source={{ uri: cachedUri }}
          style={[
            StyleSheet.absoluteFill,
            { opacity }
          ]}
          resizeMode={resizeMode}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}

      {/* Indicador de carga */}
      {isLoading && showLoadingIndicator && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator 
            size="small" 
            color={Colors.light.primary} 
          />
        </View>
      )}

      {/* Mensaje de error */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: 'transparent',
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  errorText: {
    color: Colors.light.error,
    fontSize: 12,
    textAlign: 'center',
    padding: 5,
  },
});

// Exportar componente memoizado
export default memo(OptimizedImage);
