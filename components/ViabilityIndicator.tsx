import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AlertTriangle, CheckCircle, XCircle, Info } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { ViabilityAnalysis } from '@/types/formulation';

interface ViabilityIndicatorProps {
  analysis: ViabilityAnalysis | null;
  loading?: boolean;
}

export default function ViabilityIndicator({ analysis, loading }: ViabilityIndicatorProps) {
  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingCard}>
          <Text style={styles.loadingText}>Analizando viabilidad...</Text>
        </View>
      </View>
    );
  }

  if (!analysis) return null;

  const getScoreConfig = () => {
    switch (analysis.score) {
      case 'safe':
        return {
          icon: <CheckCircle size={24} color="#4CAF50" />,
          color: '#4CAF50',
          bgColor: '#E8F5E9',
          title: 'Proceso Seguro',
          subtitle: 'El resultado es alcanzable de forma segura'
        };
      case 'caution':
        return {
          icon: <AlertTriangle size={24} color="#FF9800" />,
          color: '#FF9800',
          bgColor: '#FFF3E0',
          title: 'Precaución Necesaria',
          subtitle: 'Se requieren cuidados especiales'
        };
      case 'risky':
        return {
          icon: <XCircle size={24} color="#F44336" />,
          color: '#F44336',
          bgColor: '#FFEBEE',
          title: 'Alto Riesgo',
          subtitle: 'No recomendado sin tratamiento previo'
        };
    }
  };

  const config = getScoreConfig();

  return (
    <View style={styles.container}>
      <View style={[styles.mainCard, { backgroundColor: config.bgColor }]}>
        <View style={styles.header}>
          {config.icon}
          <View style={styles.headerText}>
            <Text style={[styles.title, { color: config.color }]}>{config.title}</Text>
            <Text style={styles.subtitle}>{config.subtitle}</Text>
          </View>
        </View>

        <View style={styles.factors}>
          <View style={styles.factorRow}>
            <Text style={styles.factorLabel}>Diferencia de niveles:</Text>
            <Text style={styles.factorValue}>{analysis.factors.levelDifference} niveles</Text>
          </View>
          <View style={styles.factorRow}>
            <Text style={styles.factorLabel}>Salud capilar:</Text>
            <Text style={styles.factorValue}>
              {analysis.factors.hairHealth === 'good' ? 'Buena' : 
               analysis.factors.hairHealth === 'fair' ? 'Regular' : 'Comprometida'}
            </Text>
          </View>
          {analysis.factors.estimatedSessions > 1 && (
            <View style={styles.factorRow}>
              <Text style={styles.factorLabel}>Sesiones estimadas:</Text>
              <Text style={styles.factorValue}>{analysis.factors.estimatedSessions}</Text>
            </View>
          )}
        </View>

        {analysis.warnings.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={16} color={config.color} />
              <Text style={[styles.sectionTitle, { color: config.color }]}>Advertencias</Text>
            </View>
            {analysis.warnings.map((warning, index) => (
              <Text key={index} style={styles.warningText}>• {warning}</Text>
            ))}
          </View>
        )}

        {analysis.recommendations.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Info size={16} color={Colors.light.primary} />
              <Text style={[styles.sectionTitle, { color: Colors.light.primary }]}>
                Recomendaciones
              </Text>
            </View>
            {analysis.recommendations.map((rec, index) => (
              <Text key={index} style={styles.recommendationText}>• {rec}</Text>
            ))}
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  loadingCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  mainCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  factors: {
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  factorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  factorLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  factorValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  section: {
    marginTop: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 24,
    marginBottom: 4,
    lineHeight: 20,
  },
  recommendationText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 24,
    marginBottom: 4,
    lineHeight: 20,
  },
});