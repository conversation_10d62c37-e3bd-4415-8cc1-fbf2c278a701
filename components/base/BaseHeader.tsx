import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import { ChevronLeft } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Colors from '@/constants/colors';
import { typography, spacing, layout } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { SyncIndicator } from '@/components/SyncIndicator';

interface BaseHeaderProps {
  title: string;
  subtitle?: string;
  onBack?: () => void;
  rightAction?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'dark' | 'transparent';
  centerTitle?: boolean;
  hideBackButton?: boolean;
  showSyncIndicator?: boolean;
}

export const BaseHeader: React.FC<BaseHeaderProps> = ({
  title,
  subtitle,
  onBack,
  rightAction,
  variant = 'primary',
  centerTitle = false,
  hideBackButton = false,
  showSyncIndicator = true,
}) => {
  const insets = useSafeAreaInsets();

  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onBack?.();
  };

  const backgroundColor = {
    primary: Colors.light.background,
    secondary: Colors.light.background,
    dark: Colors.light.background,
    transparent: 'transparent',
  }[variant];

  const textColor = Colors.light.text;

  const statusBarStyle = 'dark-content';

  return (
    <>
      <StatusBar barStyle={statusBarStyle} />
      <View 
        style={[
          styles.container,
          { 
            backgroundColor,
            paddingTop: insets.top + spacing.sm,
          },
        ]}
      >
        <View style={styles.content}>
          {/* Left Section */}
          <View style={styles.left}>
            {onBack && !hideBackButton && (
              <TouchableOpacity
                onPress={handleBack}
                style={styles.backButton}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <View style={styles.backButtonContent}>
                  <ChevronLeft size={24} color={textColor} />
                  <Text style={styles.backText}>Atrás</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>

          {/* Center Section */}
          <View style={[styles.center, centerTitle && styles.centerAbsolute]}>
            <Text 
              style={[styles.title, { color: textColor }]}
              numberOfLines={1}
            >
              {title}
            </Text>
            {subtitle && (
              <Text 
                style={[styles.subtitle, { color: textColor, opacity: 0.8 }]}
                numberOfLines={1}
              >
                {subtitle}
              </Text>
            )}
          </View>

          {/* Right Section */}
          <View style={styles.right}>
            {showSyncIndicator && !rightAction && <SyncIndicator />}
            {rightAction}
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    height: layout.headerHeight,
    justifyContent: 'flex-end',
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F7',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 1,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.sm,
    height: 56,
  },
  left: {
    flex: 1,
    alignItems: 'flex-start',
  },
  center: {
    flex: 2,
    alignItems: 'center',
  },
  centerAbsolute: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  right: {
    flex: 1,
    alignItems: 'flex-end',
  },
  backButton: {
    padding: spacing.xs,
    borderRadius: 8,
    backgroundColor: '#F5F5F7',
  },
  backButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  backText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.regular,
    marginTop: 2,
  },
});