import React, { useState, useEffect, useRef } from "react";
import { <PERSON>ert, StyleSheet, Text, View, TouchableOpacity, Modal, Animated, Dimensions, Platform } from "react-native";
import { Camera as CameraIcon, X, Check, RotateCcw, Zap } from "lucide-react-native";
import { CameraView, useCameraPermissions } from 'expo-camera';
import Colors from "@/constants/colors";
import { PhotoAngle, PhotoGuide, PHOTO_GUIDES, PhotoQuality } from "@/types/photo-capture";
import { DESIRED_PHOTO_GUIDES } from "@/types/desired-photo";
import * as Haptics from "expo-haptics";

interface GuidedCameraProps {
  visible: boolean;
  active?: boolean; // New prop to control camera session without unmounting
  currentAngle: PhotoAngle;
  onCapture: (uri: string, angle: PhotoAngle, quality: PhotoQuality) => void;
  onClose: () => void;
  onSkip?: () => void;
  mode?: 'diagnosis' | 'desired'; // Add mode to differentiate between diagnosis and desired photos
}

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

export default function GuidedCamera({
  visible,
  active = true, // Default to true for backward compatibility
  currentAngle = PhotoAngle.FRONT, // Default value to prevent undefined
  onCapture,
  onClose,
  onSkip,
  mode = 'diagnosis'
}: GuidedCameraProps) {
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<'back' | 'front'>('back');
  const [enableTorch, setEnableTorch] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const cameraRef = useRef<CameraView | null>(null);
  
  // Quality indicators
  const [lightingQuality, setLightingQuality] = useState<'good' | 'fair' | 'poor'>('fair');
  const [isStable, setIsStable] = useState(true);
  
  // Animations
  const pulseAnim = React.useRef(new Animated.Value(1)).current;
  const slideAnim = React.useRef(new Animated.Value(300)).current;

  // Component lifecycle and debugging logs
  useEffect(() => {
    console.log('[GuidedCamera] Component mounted', {
      visible,
      active,
      currentAngle,
      mode,
      platform: Platform.OS,
      platformVersion: Platform.Version,
      timestamp: new Date().toISOString()
    });
    
    return () => {
      console.log('[GuidedCamera] Component unmounting - cleanup', {
        mode,
        timestamp: new Date().toISOString()
      });
    };
  }, []);

  // Validate props and state changes
  useEffect(() => {
    console.log('[GuidedCamera] Props changed:', {
      visible,
      active,
      currentAngle,
      mode,
      guide: guide?.label,
      timestamp: new Date().toISOString()
    });
    
    if (visible) {
      console.log('[GuidedCamera] Opening with angle:', currentAngle);
      console.log('[GuidedCamera] Angle type:', typeof currentAngle);
      console.log('[GuidedCamera] Valid PhotoAngle values:', Object.values(PhotoAngle));
      console.log('[GuidedCamera] Mode:', mode);
      console.log('[GuidedCamera] Guide found:', guide ? `${guide.label} (${guide.angle})` : 'NO GUIDE');
      
      if (!currentAngle || !Object.values(PhotoAngle).includes(currentAngle)) {
        console.error('[GuidedCamera] ERROR: Invalid currentAngle!', {
          currentAngle,
          typeofAngle: typeof currentAngle,
          isInEnum: Object.values(PhotoAngle).includes(currentAngle),
          validAngles: Object.values(PhotoAngle)
        });
        
        // Use a default angle instead of closing
        console.warn('[GuidedCamera] Using default angle FRONT');
        // Don't close - let the camera work with default angle
        // The parent component should handle this properly
      }
    }
  }, [visible, active, currentAngle, mode]);

  useEffect(() => {
    if (visible && !permission?.granted) {
      requestPermission();
    }
    
    if (visible) {
      // Slide in animation
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 50,
        friction: 7
      }).start();
      
      // Pulse animation for guide
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [visible, permission]);

  const guide = mode === 'desired' 
    ? DESIRED_PHOTO_GUIDES.find(g => g.angle === currentAngle)
    : PHOTO_GUIDES.find(g => g.angle === currentAngle);
    
  console.log('[GuidedCamera] Guide selection:', {
    mode,
    currentAngle,
    foundGuide: guide?.label,
    desiredGuidesCount: DESIRED_PHOTO_GUIDES.length,
    diagnosisGuidesCount: PHOTO_GUIDES.length
  });

  const takePicture = async () => {
    console.log('[GuidedCamera] takePicture called', {
      hasRef: !!cameraRef.current,
      isCapturing,
      currentAngle,
      visible,
      active,
      timestamp: new Date().toISOString()
    });
    
    if (!cameraRef.current) {
      console.error('[GuidedCamera] Camera ref not available');
      return;
    }
    
    if (!currentAngle) {
      console.error('[GuidedCamera] Cannot take picture: currentAngle is undefined');
      return;
    }
    
    if (isCapturing) {
      console.warn('[GuidedCamera] Already capturing, ignoring duplicate request');
      return;
    }
    
    console.log('[GuidedCamera] Taking picture for angle:', currentAngle);
    setIsCapturing(true);
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.5, // Reducido para evitar problemas de memoria
        base64: false,
        skipProcessing: true,
      });
      
      console.log('[GuidedCamera] Photo captured:', {
        hasUri: !!photo?.uri,
        angle: currentAngle,
        timestamp: new Date().toISOString()
      });
      
      // Calculate quality based on current conditions
      const quality: PhotoQuality = {
        lighting: lightingQuality,
        focus: 'good', // Would need actual focus detection
        stability: isStable ? 'good' : 'fair',
        overall: calculateOverallQuality(lightingQuality, 'good', isStable ? 'good' : 'fair')
      };
      
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      onCapture(photo.uri, currentAngle, quality);
    } catch (error) {
      console.error('[GuidedCamera] Error taking picture:', error);
      console.error('[GuidedCamera] Error stack:', error?.stack);
      console.error('[GuidedCamera] Error details:', {
        name: error?.name,
        message: error?.message,
        code: error?.code
      });
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'No se pudo capturar la foto. Por favor intenta de nuevo.');
    } finally {
      setIsCapturing(false);
    }
  };

  const calculateOverallQuality = (
    lighting: 'good' | 'fair' | 'poor',
    focus: 'good' | 'fair' | 'poor',
    stability: 'good' | 'fair' | 'poor'
  ): number => {
    const scores = {
      good: 100,
      fair: 70,
      poor: 40
    };
    
    return Math.round((scores[lighting] + scores[focus] + scores[stability]) / 3);
  };

  const renderGuideOverlay = () => {
    if (!guide) return null;
    
    return (
      <View style={styles.guideOverlay}>
        <View style={styles.guideTop}>
          <Text style={styles.guideTitle}>{guide.label}</Text>
          <Text style={styles.guideDescription}>{guide.description}</Text>
        </View>
        
        <Animated.View 
          style={[
            styles.guideSilhouette,
            { transform: [{ scale: pulseAnim }] }
          ]}
        >
          <View style={[styles.guideShape, getGuideShape(currentAngle)]} />
          <Text style={styles.guideIcon}>{guide.icon}</Text>
        </Animated.View>
        
        <View style={styles.guideTips}>
          {mode === 'desired' && 'instructions' in guide && guide.instructions ? (
            <Text style={styles.tipText}>{guide.instructions}</Text>
          ) : 'tips' in guide && guide.tips ? (
            guide.tips.map((tip: string, index: number) => (
              <Text key={index} style={styles.tipText}>• {tip}</Text>
            ))
          ) : null}
        </View>
      </View>
    );
  };

  const getGuideShape = (angle: PhotoAngle) => {
    switch (angle) {
      case PhotoAngle.CROWN:
        return styles.guideShapeCircle;
      case PhotoAngle.LEFT_SIDE:
      case PhotoAngle.RIGHT_SIDE:
        return styles.guideShapeOval;
      case PhotoAngle.BACK:
      case PhotoAngle.FRONT:
        return styles.guideShapeRectangle;
      default:
        return styles.guideShapeCircle;
    }
  };

  const renderQualityIndicators = () => (
    <View style={styles.qualityContainer}>
      <View style={styles.qualityItem}>
        <View style={[
          styles.qualityDot,
          { backgroundColor: getQualityColor(lightingQuality) }
        ]} />
        <Text style={styles.qualityLabel}>Iluminación</Text>
      </View>
      <View style={styles.qualityItem}>
        <View style={[
          styles.qualityDot,
          { backgroundColor: isStable ? Colors.light.success : Colors.light.warning }
        ]} />
        <Text style={styles.qualityLabel}>Estabilidad</Text>
      </View>
    </View>
  );

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'good': return Colors.light.success;
      case 'fair': return Colors.light.warning;
      case 'poor': return Colors.light.error;
      default: return Colors.light.gray;
    }
  };

  if (!visible) return null;

  if (!permission) {
    return (
      <Modal visible={visible} animationType="slide">
        <View style={styles.container}>
          <Text>Cargando...</Text>
        </View>
      </Modal>
    );
  }

  if (!permission.granted) {
    return (
      <Modal visible={visible} animationType="slide">
        <View style={styles.container}>
          <Text style={styles.errorText}>Se requiere permiso de cámara</Text>
          <TouchableOpacity 
            style={[styles.closeButton, { backgroundColor: Colors.light.primary, padding: 16, borderRadius: 8 }]} 
            onPress={requestPermission}
          >
            <Text style={styles.closeButtonText}>Conceder Permiso</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Cancelar</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide">
      <View style={styles.container}>
        <CameraView
          style={styles.camera}
          facing={facing}
          enableTorch={enableTorch}
          ref={cameraRef}
          active={active}
        />
        
        {/* Overlay positioned absolutely over the camera */}
        <View style={styles.overlayContainer}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color="white" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.flashButton}
              onPress={() => setEnableTorch(!enableTorch)}
            >
              <Zap size={24} color={enableTorch ? Colors.light.warning : "white"} />
            </TouchableOpacity>
          </View>
          
          {renderGuideOverlay()}
          {renderQualityIndicators()}
          
          <Animated.View 
            style={[
              styles.bottomControls,
              { transform: [{ translateY: slideAnim }] }
            ]}
          >
            <View style={styles.controlsContent}>
              {guide && !guide.required && onSkip && (
                <TouchableOpacity style={styles.skipButton} onPress={onSkip}>
                  <Text style={styles.skipText}>Omitir</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={[styles.captureButton, isCapturing && styles.captureButtonActive]}
                onPress={takePicture}
                disabled={isCapturing}
              >
                <View style={styles.captureButtonInner}>
                  <CameraIcon size={32} color="white" />
                </View>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.flipButton}
                onPress={() => setFacing(facing === 'back' ? 'front' : 'back')}
              >
                <RotateCcw size={24} color="white" />
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "black",
  },
  camera: {
    flex: 1,
  },
  overlayContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "transparent",
  },
  cameraContent: {
    flex: 1,
    backgroundColor: "transparent",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  flashButton: {
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  guideOverlay: {
    flex: 1,
    justifyContent: "space-between",
    paddingHorizontal: 20,
  },
  guideTop: {
    alignItems: "center",
    marginTop: 20,
  },
  guideTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "white",
    marginBottom: 8,
    textShadowColor: "rgba(0,0,0,0.7)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  guideDescription: {
    fontSize: 16,
    color: "white",
    textAlign: "center",
    opacity: 0.9,
    textShadowColor: "rgba(0,0,0,0.7)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  guideSilhouette: {
    alignItems: "center",
    justifyContent: "center",
  },
  guideShape: {
    borderWidth: 3,
    borderColor: "rgba(255,255,255,0.5)",
    borderStyle: "dashed",
  },
  guideShapeCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
  },
  guideShapeOval: {
    width: 150,
    height: 250,
    borderRadius: 75,
  },
  guideShapeRectangle: {
    width: 200,
    height: 280,
    borderRadius: 20,
  },
  guideIcon: {
    position: "absolute",
    fontSize: 48,
    opacity: 0.3,
  },
  guideTips: {
    backgroundColor: "rgba(0,0,0,0.7)",
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  tipText: {
    color: "white",
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  qualityContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 24,
    marginBottom: 20,
  },
  qualityItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(0,0,0,0.5)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  qualityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  qualityLabel: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
  },
  bottomControls: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0,0,0,0.8)",
    paddingBottom: 40,
    paddingTop: 20,
  },
  controlsContent: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 4,
    borderColor: "white",
  },
  captureButtonActive: {
    opacity: 0.7,
    transform: [{ scale: 0.95 }],
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.light.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  skipButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  skipText: {
    color: "white",
    fontSize: 16,
    opacity: 0.7,
  },
  flipButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    color: Colors.light.error,
    textAlign: "center",
    marginBottom: 20,
  },
  closeButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});