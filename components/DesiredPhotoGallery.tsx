import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Animated, Platform, ActivityIndicator, Alert } from "react-native";
import { Camera, Upload, X, <PERSON>rkles, Zap, Scan<PERSON>ine, HelpCircle } from "lucide-react-native";
import Colors from "@/constants/colors";
import { DesiredPhoto, DesiredPhotoType, DesiredPhotoGuide, DESIRED_PHOTO_GUIDES } from "@/types/desired-photo";
import { useAIAnalysisStore, useDesiredPhotoAnalyses } from "@/stores/ai-analysis-store";
import { PhotoAnalysisResult } from "@/types/lifestyle-preferences";

interface DesiredPhotoGalleryProps {
  photos: DesiredPhoto[];
  onAddPhoto: (isCamera: boolean, useGuidedCapture?: boolean, photoType?: DesiredPhotoType) => void;
  onRemovePhoto: (photoId: string) => void;
  onPhotoAnalyzed?: (photoId: string, analysis: PhotoAnalysisResult) => void;
  currentLevel?: number; // Current hair level for viability calculation
  maxPhotos?: number;
}

// Componente memoizado para el análisis de foto
const PhotoAnalysisDisplay = memo(({
  analysis
}: {
  analysis: any
}) => (
  <View style={styles.analysisResult}>
    <View style={styles.analysisRow}>
      <Text style={styles.analysisLabel}>Nivel detectado:</Text>
      <Text style={styles.analysisValue}>{analysis.detectedLevel}</Text>
    </View>
    <View style={styles.analysisRow}>
      <Text style={styles.analysisLabel}>Técnica:</Text>
      <Text style={styles.analysisValue}>{analysis.detectedTechnique}</Text>
    </View>
    <View style={[
      styles.viabilityBadge,
      { backgroundColor: analysis.viabilityScore > 70
          ? Colors.light.success + '20'
          : analysis.viabilityScore > 40
          ? Colors.light.warning + '20'
          : Colors.light.error + '20'
      }
    ]}>
      <Text style={[
        styles.viabilityText,
        { color: analysis.viabilityScore > 70
            ? Colors.light.success
            : analysis.viabilityScore > 40
            ? Colors.light.warning
            : Colors.light.error
        }
      ]}>
        Viabilidad: {analysis.viabilityScore}%
      </Text>
    </View>
    {analysis.warnings && (
      <Text style={styles.warningText}>
        ⚠️ {analysis.warnings[0]}
      </Text>
    )}
  </View>
));

PhotoAnalysisDisplay.displayName = 'PhotoAnalysisDisplay';

// Componente memoizado para cada slot de foto
const DesiredPhotoSlot = memo(({
  photo,
  index,
  guide,
  scaleAnim,
  isSlotFilled,
  maxPhotos,
  supportsGuidedCamera,
  analyzingPhotoId,
  analysis,
  onAddPhoto,
  onRemovePhoto,
  onAnalyzePhoto
}: {
  photo: DesiredPhoto | null;
  index: number;
  guide: DesiredPhotoGuide | undefined;
  scaleAnim: Animated.Value;
  isSlotFilled: boolean;
  maxPhotos: number;
  supportsGuidedCamera: boolean;
  analyzingPhotoId: string | null;
  analysis: any;
  onAddPhoto: (isCamera: boolean, useGuidedCapture?: boolean, photoType?: DesiredPhotoType) => void;
  onRemovePhoto: (photoId: string) => void;
  onAnalyzePhoto: (photo: DesiredPhoto) => void;
}) => {
  const handleRemovePhoto = useCallback(() => {
    if (photo) {
      onRemovePhoto(photo.id);
    }
  }, [photo, onRemovePhoto]);

  const handleAnalyzePhoto = useCallback(() => {
    if (photo) {
      onAnalyzePhoto(photo);
    }
  }, [photo, onAnalyzePhoto]);

  const handleAddPhoto = useCallback(() => {
    onAddPhoto(true, supportsGuidedCamera, guide?.type);
  }, [onAddPhoto, supportsGuidedCamera, guide]);

  const showHelpAlert = useCallback(() => {
    if (guide?.helpText) {
      Alert.alert(guide.label, guide.helpText);
    }
  }, [guide]);

  if (!guide) return null;

  if (!photo && !isSlotFilled && index < maxPhotos) {
    // Empty slot
    return (
      <TouchableOpacity
        key={`empty-${guide.type}`}
        style={styles.photoSlot}
        onPress={handleAddPhoto}
      >
        <View style={styles.emptySlot}>
          <Text style={styles.slotIcon}>{guide.icon}</Text>
          <Text style={styles.slotLabel}>{guide.label}</Text>
          {guide.required && (
            <View style={styles.requiredBadge}>
              <Text style={styles.requiredText}>Requerida</Text>
            </View>
          )}
          {guide.helpText && (
            <TouchableOpacity
              style={styles.helpButton}
              onPress={showHelpAlert}
            >
              <HelpCircle size={16} color={Colors.light.gray} />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  }

  if (photo) {
    // Filled slot
    return (
      <Animated.View
        key={photo.id}
        style={[
          styles.photoSlot,
          {
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <Image source={{ uri: photo.uri }} style={styles.photoImage} />

        <View style={styles.photoOverlay}>
          <View style={styles.photoHeader}>
            <View style={styles.photoTypeTag}>
              <Text style={styles.photoTypeIcon}>{guide?.icon}</Text>
              <Text style={styles.photoTypeText}>{guide?.label}</Text>
            </View>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={handleRemovePhoto}
            >
              <X size={18} color="white" />
            </TouchableOpacity>
          </View>

          <View style={styles.photoFooter}>
            {!analysis && (
              <TouchableOpacity
                style={styles.analyzeButton}
                onPress={handleAnalyzePhoto}
                disabled={analyzingPhotoId === photo.id}
              >
                {analyzingPhotoId === photo.id ? (
                  <ActivityIndicator size="small" color={Colors.light.primary} />
                ) : (
                  <>
                    <ScanLine size={16} color={Colors.light.primary} />
                    <Text style={styles.analyzeButtonText}>Analizar foto</Text>
                  </>
                )}
              </TouchableOpacity>
            )}

            {analysis && <PhotoAnalysisDisplay analysis={analysis} />}
          </View>
        </View>
      </Animated.View>
    );
  }

  return null;
});

DesiredPhotoSlot.displayName = 'DesiredPhotoSlot';

function DesiredPhotoGallery({
  photos,
  onAddPhoto,
  onRemovePhoto,
  onPhotoAnalyzed,
  currentLevel = 6, // Default to level 6 if not provided
  maxPhotos = 5
}: DesiredPhotoGalleryProps) {
  const isIOSPlatform = Platform.OS === 'ios';
  const supportsGuidedCamera = !isIOSPlatform;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const { analyzeDesiredPhoto } = useAIAnalysisStore();
  const desiredPhotoAnalyses = useDesiredPhotoAnalyses();
  const [analyzingPhotoId, setAnalyzingPhotoId] = useState<string | null>(null);

  // Memoizar los callbacks para evitar re-renders innecesarios
  const handleAddPhotoCallback = useCallback((isCamera: boolean, useGuidedCapture?: boolean, photoType?: DesiredPhotoType) => {
    onAddPhoto(isCamera, useGuidedCapture, photoType);
  }, [onAddPhoto]);

  const handleRemovePhotoCallback = useCallback((photoId: string) => {
    onRemovePhoto(photoId);
  }, [onRemovePhoto]);

  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 50,
      friction: 7
    }).start();
  }, [photos.length]);

  const handleAnalyzePhoto = useCallback(async (photo: DesiredPhoto) => {
    setAnalyzingPhotoId(photo.id);

    try {
      const result = await analyzeDesiredPhoto(photo.id, photo.uri, currentLevel);

      if (result && onPhotoAnalyzed) {
        onPhotoAnalyzed(photo.id, result as PhotoAnalysisResult);
      }
    } catch (error) {
      console.error('Error analyzing photo:', error);
    } finally {
      setAnalyzingPhotoId(null);
    }
  }, [analyzeDesiredPhoto, currentLevel, onPhotoAnalyzed]);

  // Memoizar la función que renderiza cada slot de foto
  const renderPhotoSlot = useCallback((photo: DesiredPhoto | null, index: number) => {
    // For empty slots, use the guide at the index position
    // For filled slots, find the guide that matches the photo type
    const guide = photo
      ? DESIRED_PHOTO_GUIDES.find(g => g.type === photo.type)
      : DESIRED_PHOTO_GUIDES[index];

    if (!guide) return null;

    // Check if this slot is already filled
    const isSlotFilled = photos.some(p => p.type === guide.type);
    const analysis = photo ? desiredPhotoAnalyses[photo.id] : null;

    return (
      <DesiredPhotoSlot
        key={photo ? photo.id : `empty-${guide.type}`}
        photo={photo}
        index={index}
        guide={guide}
        scaleAnim={scaleAnim}
        isSlotFilled={isSlotFilled}
        maxPhotos={maxPhotos}
        supportsGuidedCamera={supportsGuidedCamera}
        analyzingPhotoId={analyzingPhotoId}
        analysis={analysis}
        onAddPhoto={handleAddPhotoCallback}
        onRemovePhoto={handleRemovePhotoCallback}
        onAnalyzePhoto={handleAnalyzePhoto}
      />
    );
  }, [
    photos,
    desiredPhotoAnalyses,
    scaleAnim,
    maxPhotos,
    supportsGuidedCamera,
    analyzingPhotoId,
    handleAddPhotoCallback,
    handleRemovePhotoCallback,
    handleAnalyzePhoto
  ]);

  // Memoizar los slots de fotos para evitar recálculos
  const photoSlots = useMemo(() => {
    return DESIRED_PHOTO_GUIDES.map((guide, index) => {
      const photo = photos.find(p => p.type === guide.type);
      // Only show slots up to maxPhotos limit
      if (index < maxPhotos) {
        return renderPhotoSlot(photo, index);
      }
      return null;
    }).filter(Boolean);
  }, [photos, maxPhotos, renderPhotoSlot]);

  const renderPhotoGallery = useCallback(() => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {photoSlots}
      </ScrollView>
    );
  }, [photoSlots]);

  // Memoizar los handlers de los botones de acción
  const handleCameraAction = useCallback(() => {
    handleAddPhotoCallback(true, supportsGuidedCamera);
  }, [handleAddPhotoCallback, supportsGuidedCamera]);

  const handleUploadAction = useCallback(() => {
    handleAddPhotoCallback(false);
  }, [handleAddPhotoCallback]);

  // Memoizar el estado de los botones
  const isMaxPhotosReached = useMemo(() => photos.length >= maxPhotos, [photos.length, maxPhotos]);

  return (
    <View style={styles.container}>
      {renderPhotoGallery()}

      {/* Action buttons - similar to PhotoGallery */}
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleCameraAction}
          disabled={isMaxPhotosReached}
        >
          <Camera size={16} color="white" />
          <Text style={styles.actionButtonText}>
            {supportsGuidedCamera ? "Captura Guiada" : "Tomar Foto"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonSecondary]}
          onPress={handleUploadAction}
          disabled={isMaxPhotosReached}
        >
          <Upload size={16} color={Colors.light.primary} />
          <Text style={[styles.actionButtonText, styles.actionButtonTextSecondary]}>Subir Fotos</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.helpText}>
        {photos.length} de 3-{maxPhotos} fotos • Desliza para ver todas las fotos
      </Text>
    </View>
  );
}

// Exportar el componente memoizado
export default memo(DesiredPhotoGallery);

// Estilos memoizados para evitar recreaciones
const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  compactContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    overflow: "hidden",
  },
  compactEmpty: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  compactEmptyText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
    marginTop: 12,
    marginBottom: 4,
  },
  compactEmptySubtext: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 24,
  },
  compactActions: {
    flexDirection: "row",
    gap: 8,
    flexWrap: "wrap",
    justifyContent: "center",
  },
  compactButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 6,
    minWidth: 120,
  },
  compactButtonSecondary: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  compactButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 13,
  },
  compactButtonTextSecondary: {
    color: Colors.light.primary,
  },
  scrollContent: {
    paddingHorizontal: 4,
    gap: 12,
  },
  photoSlot: {
    width: 140,
    height: 180,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: Colors.light.surface,
  },
  emptySlot: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: "dashed",
    borderRadius: 12,
    padding: 12,
  },
  slotIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  slotLabel: {
    fontSize: 13,
    fontWeight: "500",
    color: Colors.light.text,
    textAlign: "center",
  },
  requiredBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: Colors.light.primary + "20",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  requiredText: {
    fontSize: 10,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  actionButtons: {
    position: "absolute",
    bottom: 8,
    left: 8,
    right: 8,
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 6,
    paddingVertical: 6,
    gap: 4,
  },
  actionButtonSecondary: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  actionButtonText: {
    color: "white",
    fontSize: 11,
    fontWeight: "500",
  },
  actionButtonTextSecondary: {
    color: Colors.light.gray,
  },
  photoImage: {
    width: "100%",
    height: "100%",
  },
  photoOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "space-between",
    padding: 8,
  },
  photoHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  photoTypeTag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255,255,255,0.9)",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  photoTypeIcon: {
    fontSize: 12,
  },
  photoTypeText: {
    fontSize: 11,
    fontWeight: "600",
    color: Colors.light.text,
  },
  removeButton: {
    backgroundColor: "#FF4444",
    borderRadius: 14,
    width: 28,
    height: 28,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(255,255,255,0.3)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  photoFooter: {
    alignItems: "flex-start",
  },
  viabilityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  viabilityText: {
    fontSize: 11,
    fontWeight: "600",
  },
  addMoreSlot: {
    borderWidth: 2,
    borderColor: Colors.light.primary + "30",
    borderStyle: "solid",
    backgroundColor: Colors.light.primary + "10",
    justifyContent: "center",
    alignItems: "center",
    gap: 4,
  },
  addMoreText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: "center",
    marginTop: 12,
    fontStyle: "italic",
  },
  actionButtonsContainer: {
    flexDirection: "row",
    gap: 12,
    marginTop: 16,
    paddingHorizontal: 4,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
  },
  actionButtonSecondary: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  actionButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  actionButtonTextSecondary: {
    color: Colors.light.primary,
  },
  helpButton: {
    position: "absolute",
    top: 8,
    left: 8,
    padding: 4,
  },
  analyzeButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.primary + "10",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
    marginTop: 8,
  },
  analyzeButtonText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  analysisResult: {
    marginTop: 8,
    backgroundColor: "rgba(0,0,0,0.3)",
    borderRadius: 8,
    padding: 8,
  },
  analysisRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  analysisLabel: {
    fontSize: 11,
    color: "rgba(255,255,255,0.8)",
  },
  analysisValue: {
    fontSize: 11,
    fontWeight: "600",
    color: "white",
  },
  warningText: {
    fontSize: 10,
    color: Colors.light.warning,
    marginTop: 4,
    fontStyle: "italic",
  },
});