import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ChevronDown, ChevronUp, DollarSign, Package, TrendingUp } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { FormulaCost } from '@/types/formulation';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';

interface FormulaCostBreakdownProps {
  cost: FormulaCost | null;
  loading?: boolean;
  isRealCost?: boolean;
}

export default function FormulaCostBreakdown({ cost, loading, isRealCost = false }: FormulaCostBreakdownProps) {
  const { formatCurrency, formatVolume, formatWeight } = useRegionalUnits();
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Helper function to format amounts with regional units
  const formatAmount = (amount: string): string => {
    const match = amount.match(/(\d+(?:\.\d+)?)(ml|g)/);
    if (match) {
      const value = parseFloat(match[1]);
      const unit = match[2];
      if (unit === 'ml') {
        return formatVolume(value);
      } else if (unit === 'g') {
        return formatWeight(value);
      }
    }
    return amount;
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingCard}>
          <Text style={styles.loadingText}>Calculando costes...</Text>
        </View>
      </View>
    );
  }

  if (!cost) return null;

  const profitPercentage = ((cost.profitMargin / cost.suggestedServicePrice) * 100).toFixed(0);
  const profitPercentageNum = parseFloat(profitPercentage);
  
  // Determine margin color based on percentage
  let marginColor = Colors.light.success; // Green for >70%
  if (profitPercentageNum < 50) {
    marginColor = Colors.light.error; // Red for <50%
  } else if (profitPercentageNum < 70) {
    marginColor = Colors.light.warning; // Yellow for 50-70%
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.headerLeft}>
          <DollarSign size={20} color={Colors.light.primary} />
          <Text style={styles.headerTitle}>Desglose de Costes</Text>
          {isRealCost ? (
            <View style={styles.realCostBadge}>
              <Text style={styles.realCostBadgeText}>Costo Real</Text>
            </View>
          ) : (
            <View style={styles.estimatedCostBadge}>
              <Text style={styles.estimatedCostBadgeText}>Estimado</Text>
            </View>
          )}
        </View>
        <View style={styles.headerRight}>
          <Text style={styles.totalCost}>{formatCurrency(cost.totalMaterialCost)}</Text>
          {isExpanded ? (
            <ChevronUp size={20} color={Colors.light.gray} />
          ) : (
            <ChevronDown size={20} color={Colors.light.gray} />
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          <View style={styles.itemsSection}>
            <Text style={styles.sectionTitle}>Materiales utilizados</Text>
            {cost.items.map((item, index) => (
              <View key={index} style={styles.item}>
                <View style={styles.itemLeft}>
                  <Package size={16} color={Colors.light.gray} />
                  <View style={styles.itemInfo}>
                    <Text style={styles.itemName}>{item.product}</Text>
                    <Text style={styles.itemAmount}>{formatAmount(item.amount)}</Text>
                  </View>
                </View>
                <Text style={styles.itemCost}>{formatCurrency(item.totalCost)}</Text>
              </View>
            ))}
          </View>

          <View style={styles.divider} />

          <View style={styles.summarySection}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Coste total materiales</Text>
              <Text style={styles.summaryValue}>{formatCurrency(cost.totalMaterialCost)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Precio sugerido servicio</Text>
              <Text style={[styles.summaryValue, styles.suggestedPrice]}>
                {formatCurrency(cost.suggestedServicePrice)}
              </Text>
            </View>
            <View style={[styles.summaryRow, styles.profitRow]}>
              <View style={styles.profitLabel}>
                <TrendingUp size={16} color={marginColor} />
                <Text style={styles.summaryLabel}>Margen de beneficio</Text>
              </View>
              <View style={styles.profitValue}>
                <Text style={[styles.summaryValue, { color: marginColor }]}>
                  {formatCurrency(cost.profitMargin)}
                </Text>
                <Text style={[styles.profitPercentage, { color: marginColor }]}>({profitPercentage}%)</Text>
              </View>
            </View>
          </View>

          <View style={styles.note}>
            <Text style={styles.noteText}>
              * {isRealCost ? 'Precios basados en tu inventario actual' : 'Precios estimados - configura tu inventario para costos exactos'}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  loadingCard: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  totalCost: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  itemsSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 12,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemInfo: {
    marginLeft: 12,
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  itemAmount: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  itemCost: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: 16,
  },
  summarySection: {
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  suggestedPrice: {
    color: Colors.light.primary,
  },
  profitRow: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  profitLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  profitValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  profitText: {
    color: '#4CAF50',
  },
  profitPercentage: {
    fontSize: 14,
    color: '#4CAF50',
  },
  note: {
    marginTop: 16,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 8,
  },
  noteText: {
    fontSize: 12,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  realCostBadge: {
    backgroundColor: Colors.light.success + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  realCostBadgeText: {
    fontSize: 11,
    color: Colors.light.success,
    fontWeight: '600',
  },
  estimatedCostBadge: {
    backgroundColor: Colors.light.warning + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  estimatedCostBadgeText: {
    fontSize: 11,
    color: Colors.light.warning,
    fontWeight: '600',
  },
});