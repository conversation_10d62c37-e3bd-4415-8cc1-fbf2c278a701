import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import {
  Package,
  AlertCircle,
  TrendingUp,
  DollarSign,
  BarChart3,
  ChevronRight,
  Calendar,
  ShoppingCart,
} from "lucide-react-native";
import Colors from "@/constants/colors";
import { useInventoryStore } from "@/stores/inventory-store";
import { useSalonConfigStore } from "@/stores/salon-config-store";
import { InventoryReport } from "@/types/inventory";

interface InventoryReportsProps {
  onProductSelect?: (productId: string) => void;
}

export default function InventoryReports({ onProductSelect }: InventoryReportsProps) {
  const [report, setReport] = useState<InventoryReport | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { generateInventoryReport, getActiveAlerts } = useInventoryStore();
  const { formatCurrency } = useSalonConfigStore();

  useEffect(() => {
    loadReport();
  }, []);

  const loadReport = () => {
    setIsLoading(true);
    try {
      const reportData = generateInventoryReport();
      setReport(reportData);
    } catch (error) {
      console.error("Error loading report:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
      </View>
    );
  }

  if (!report) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No hay datos disponibles</Text>
      </View>
    );
  }

  const alerts = getActiveAlerts();
  const criticalAlerts = alerts.filter((a) => a.severity === "high");

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Summary Cards */}
      <View style={styles.summarySection}>
        <Text style={styles.sectionTitle}>Resumen de Inventario</Text>
        
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, styles.primaryCard]}>
            <View style={styles.cardIcon}>
              <DollarSign size={24} color="white" />
            </View>
            <Text style={styles.cardValue}>{formatCurrency(report.totalValue)}</Text>
            <Text style={styles.cardLabel}>Valor Total</Text>
          </View>

          <View style={[styles.summaryCard, styles.warningCard]}>
            <View style={styles.cardIcon}>
              <AlertCircle size={24} color="white" />
            </View>
            <Text style={styles.cardValue}>{report.lowStockCount}</Text>
            <Text style={styles.cardLabel}>Stock Bajo</Text>
          </View>

          <View style={[styles.summaryCard, styles.errorCard]}>
            <View style={styles.cardIcon}>
              <Package size={24} color="white" />
            </View>
            <Text style={styles.cardValue}>{report.outOfStockCount}</Text>
            <Text style={styles.cardLabel}>Sin Stock</Text>
          </View>

          <View style={[styles.summaryCard, styles.successCard]}>
            <View style={styles.cardIcon}>
              <ShoppingCart size={24} color="white" />
            </View>
            <Text style={styles.cardValue}>{report.overstockCount}</Text>
            <Text style={styles.cardLabel}>Exceso Stock</Text>
          </View>
        </View>
      </View>

      {/* Critical Alerts */}
      {criticalAlerts.length > 0 && (
        <View style={styles.alertsSection}>
          <Text style={styles.sectionTitle}>Alertas Críticas</Text>
          {criticalAlerts.slice(0, 3).map((alert) => (
            <View key={alert.id} style={styles.alertCard}>
              <AlertCircle size={20} color={Colors.light.error} />
              <Text style={styles.alertText}>{alert.message}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Most Used Products */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <TrendingUp size={20} color={Colors.light.primary} />
          <Text style={styles.sectionTitle}>Productos Más Usados (30 días)</Text>
        </View>
        
        {report.mostUsedProducts.length > 0 ? (
          report.mostUsedProducts.slice(0, 5).map((item, index) => (
            <TouchableOpacity
              key={item.product.id}
              style={styles.productRow}
              onPress={() => onProductSelect?.(item.product.id)}
            >
              <View style={styles.rankBadge}>
                <Text style={styles.rankText}>{index + 1}</Text>
              </View>
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{item.product.name}</Text>
                <Text style={styles.productBrand}>{item.product.brand}</Text>
                <View style={styles.productStats}>
                  <Text style={styles.statText}>
                    {item.totalConsumed} {item.product.unitType} consumidos
                  </Text>
                  <Text style={styles.statSeparator}>•</Text>
                  <Text style={styles.statText}>{item.usageCount} usos</Text>
                </View>
              </View>
              <ChevronRight size={20} color={Colors.light.gray} />
            </TouchableOpacity>
          ))
        ) : (
          <Text style={styles.emptyListText}>
            No hay datos de consumo en los últimos 30 días
          </Text>
        )}
      </View>

      {/* Least Used Products */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Calendar size={20} color={Colors.light.warning} />
          <Text style={styles.sectionTitle}>Productos Sin Movimiento</Text>
        </View>
        
        {report.leastUsedProducts.length > 0 ? (
          report.leastUsedProducts.slice(0, 5).map((item) => (
            <TouchableOpacity
              key={item.product.id}
              style={styles.productRow}
              onPress={() => onProductSelect?.(item.product.id)}
            >
              <View style={[styles.rankBadge, styles.warningBadge]}>
                <Calendar size={16} color={Colors.light.warning} />
              </View>
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{item.product.name}</Text>
                <Text style={styles.productBrand}>{item.product.brand}</Text>
                <Text style={styles.warningText}>
                  {item.daysSinceLastUse === Infinity
                    ? "Nunca usado"
                    : `Sin uso hace ${item.daysSinceLastUse} días`}
                </Text>
              </View>
              <ChevronRight size={20} color={Colors.light.gray} />
            </TouchableOpacity>
          ))
        ) : (
          <Text style={styles.emptyListText}>
            Todos los productos tienen movimiento reciente
          </Text>
        )}
      </View>

      {/* Cost by Category */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <BarChart3 size={20} color={Colors.light.secondary} />
          <Text style={styles.sectionTitle}>Valor por Categoría</Text>
        </View>
        
        {report.costByCategory.map((category) => (
          <View key={category.category} style={styles.categoryRow}>
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryName}>
                {category.category.charAt(0).toUpperCase() + category.category.slice(1)}
              </Text>
              <Text style={styles.categoryValue}>
                {formatCurrency(category.totalCost)}
              </Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${category.percentage}%` },
                ]}
              />
            </View>
            <Text style={styles.percentageText}>{category.percentage.toFixed(1)}%</Text>
          </View>
        ))}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Reporte generado el {new Date(report.generatedAt).toLocaleDateString()}
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.light.gray,
  },
  summarySection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: "47%",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  primaryCard: {
    backgroundColor: Colors.light.primary,
  },
  warningCard: {
    backgroundColor: Colors.light.warning,
  },
  errorCard: {
    backgroundColor: Colors.light.error,
  },
  successCard: {
    backgroundColor: Colors.light.success,
  },
  cardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  cardValue: {
    fontSize: 24,
    fontWeight: "700",
    color: "white",
    marginBottom: 4,
  },
  cardLabel: {
    fontSize: 14,
    color: "white",
    opacity: 0.9,
  },
  alertsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  alertCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.error + "15",
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: Colors.light.error,
  },
  alertText: {
    fontSize: 14,
    color: Colors.light.error,
    marginLeft: 8,
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 16,
  },
  productRow: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  rankBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + "20",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  warningBadge: {
    backgroundColor: Colors.light.warning + "20",
  },
  rankText: {
    fontSize: 14,
    fontWeight: "700",
    color: Colors.light.primary,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  productBrand: {
    fontSize: 14,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  productStats: {
    flexDirection: "row",
    alignItems: "center",
  },
  statText: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  statSeparator: {
    marginHorizontal: 6,
    color: Colors.light.lightGray,
  },
  warningText: {
    fontSize: 12,
    color: Colors.light.warning,
    fontWeight: "500",
  },
  emptyListText: {
    fontSize: 14,
    color: Colors.light.gray,
    textAlign: "center",
    paddingVertical: 20,
  },
  categoryRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  categoryInfo: {
    width: 120,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 2,
  },
  categoryValue: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  progressBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.light.lightGray,
    borderRadius: 4,
    marginHorizontal: 12,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: Colors.light.secondary,
    borderRadius: 4,
  },
  percentageText: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.light.secondary,
    width: 45,
    textAlign: "right",
  },
  footer: {
    alignItems: "center",
    paddingVertical: 20,
  },
  footerText: {
    fontSize: 12,
    color: Colors.light.gray,
  },
});