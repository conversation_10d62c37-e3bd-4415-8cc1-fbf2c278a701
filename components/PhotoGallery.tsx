import React, { memo, useMemo, useCallback, useRef, useEffect } from "react";
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image, Animated } from "react-native";
import { Camera, X, AlertTriangle, CheckCircle } from "lucide-react-native";
import Colors from "@/constants/colors";
import { CapturedPhoto, PhotoGuide, PHOTO_GUIDES } from "@/types/photo-capture";

interface PhotoGalleryProps {
  photos: CapturedPhoto[];
  onAddPhoto: () => void;
  onRemovePhoto: (photoId: string) => void;
  onRetakePhoto: (photo: CapturedPhoto) => void;
  maxPhotos?: number;
}

// Componente memoizado para cada foto individual
const PhotoSlot = memo(({
  photo,
  index,
  guide,
  scaleAnim,
  onAddPhoto,
  onRemovePhoto,
  onRetakePhoto
}: {
  photo: CapturedPhoto | null;
  index: number;
  guide: PhotoGuide | undefined;
  scaleAnim: Animated.Value;
  onAddPhoto: () => void;
  onRemovePhoto: (photoId: string) => void;
  onRetakePhoto: (photo: CapturedPhoto) => void;
}) => {
  const getQualityIcon = useCallback((overallQuality: number) => {
    if (overallQuality >= 80) {
      return <CheckCircle size={16} color={Colors.light.success} />;
    } else if (overallQuality >= 60) {
      return <AlertTriangle size={16} color={Colors.light.warning} />;
    } else {
      return <AlertTriangle size={16} color={Colors.light.error} />;
    }
  }, []);

  const handleRemovePhoto = useCallback(() => {
    if (photo) {
      onRemovePhoto(photo.id);
    }
  }, [photo, onRemovePhoto]);

  const handleRetakePhoto = useCallback(() => {
    if (photo) {
      onRetakePhoto(photo);
    }
  }, [photo, onRetakePhoto]);

  if (!photo) {
    // Empty slot
    return (
      <TouchableOpacity
        key={`empty-${index}`}
        style={styles.photoSlot}
        onPress={onAddPhoto}
      >
        <View style={styles.emptySlot}>
          <Text style={styles.slotIcon}>{guide?.icon || '📷'}</Text>
          <Text style={styles.slotLabel}>{guide?.label || 'Agregar'}</Text>
          {guide?.required && (
            <View style={styles.requiredBadge}>
              <Text style={styles.requiredText}>Requerida</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  }

  // Filled slot
  return (
    <Animated.View
      key={photo.id}
      style={[
        styles.photoSlot,
        {
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      <Image source={{ uri: photo.uri }} style={styles.photoImage} />

      <View style={styles.photoOverlay}>
        <View style={styles.photoHeader}>
          <View style={styles.qualityIndicator}>
            {getQualityIcon(photo.quality.overall)}
          </View>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={handleRemovePhoto}
          >
            <X size={16} color="white" />
          </TouchableOpacity>
        </View>

        <View style={styles.photoFooter}>
          <Text style={styles.photoLabel}>{guide?.label}</Text>
          {photo.quality.overall < 60 && (
            <TouchableOpacity
              style={styles.retakeButton}
              onPress={handleRetakePhoto}
            >
              <Camera size={12} color="white" />
              <Text style={styles.retakeText}>Retomar</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Animated.View>
  );
});

PhotoSlot.displayName = 'PhotoSlot';

function PhotoGallery({
  photos,
  onAddPhoto,
  onRemovePhoto,
  onRetakePhoto,
  maxPhotos = 5
}: PhotoGalleryProps) {
  const scaleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 50,
      friction: 7
    }).start();
  }, [photos.length]);

  // Memoizar los callbacks para evitar re-renders innecesarios
  const handleRemovePhoto = useCallback((photoId: string) => {
    onRemovePhoto(photoId);
  }, [onRemovePhoto]);

  const handleRetakePhoto = useCallback((photo: CapturedPhoto) => {
    onRetakePhoto(photo);
  }, [onRetakePhoto]);

  const handleAddPhoto = useCallback(() => {
    onAddPhoto();
  }, [onAddPhoto]);

  // Memoizar la función que renderiza cada slot de foto
  const renderPhotoSlot = useCallback((photo: CapturedPhoto | null, index: number) => {
    const guide = photo
      ? PHOTO_GUIDES.find(g => g.angle === photo.angle)
      : PHOTO_GUIDES[index < PHOTO_GUIDES.length ? index : 0];

    return (
      <PhotoSlot
        key={photo ? photo.id : `empty-${index}`}
        photo={photo}
        index={index}
        guide={guide}
        scaleAnim={scaleAnim}
        onAddPhoto={handleAddPhoto}
        onRemovePhoto={handleRemovePhoto}
        onRetakePhoto={handleRetakePhoto}
      />
    );
  }, [scaleAnim, handleAddPhoto, handleRemovePhoto, handleRetakePhoto]);

  // Memoizar cálculos costosos del progreso
  const progressData = useMemo(() => {
    const requiredCount = PHOTO_GUIDES.filter(g => g.required).length;
    const capturedRequired = photos.filter(p => {
      const guide = PHOTO_GUIDES.find(g => g.angle === p.angle);
      return guide?.required;
    }).length;

    return {
      requiredCount,
      capturedRequired,
      progressPercentage: (capturedRequired / requiredCount) * 100
    };
  }, [photos]);

  const renderProgressIndicator = useCallback(() => {
    const { requiredCount, capturedRequired, progressPercentage } = progressData;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>
            {photos.length} de {requiredCount}-{maxPhotos} fotos
          </Text>
          <Text style={styles.progressSubtitle}>
            {capturedRequired}/{requiredCount} requeridas
          </Text>
        </View>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${progressPercentage}%` }
            ]}
          />
        </View>
      </View>
    );
  }, [progressData, photos.length, maxPhotos]);

  // Memoizar los slots vacíos para evitar recálculos
  const emptySlots = useMemo(() => {
    if (photos.length >= maxPhotos) return [];

    return PHOTO_GUIDES.slice(photos.length, maxPhotos).map((_, index) =>
      photos.length + index
    );
  }, [photos.length, maxPhotos]);

  // Memoizar la condición del botón "Agregar más"
  const showAddMoreButton = useMemo(() => {
    const requiredCount = PHOTO_GUIDES.filter(g => g.required).length;
    return photos.length < maxPhotos && photos.length >= requiredCount;
  }, [photos.length, maxPhotos]);

  return (
    <View style={styles.container}>
      {renderProgressIndicator()}

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Render captured photos first */}
        {photos.map((photo, index) => renderPhotoSlot(photo, index))}

        {/* Render empty slots for remaining guides */}
        {emptySlots.map(index => renderPhotoSlot(null, index))}

        {/* Add more button if not at max */}
        {showAddMoreButton && (
          <TouchableOpacity
            style={[styles.photoSlot, styles.addMoreSlot]}
            onPress={handleAddPhoto}
          >
            <Camera size={24} color={Colors.light.primary} />
            <Text style={styles.addMoreText}>Agregar más</Text>
          </TouchableOpacity>
        )}
      </ScrollView>

      <Text style={styles.helpText}>
        Desliza para ver todas las fotos • Toca para agregar o editar
      </Text>
    </View>
  );
}

// Exportar el componente memoizado
export default memo(PhotoGallery);

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  progressContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.light.text,
  },
  progressSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.light.border,
    borderRadius: 3,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: Colors.light.primary,
    borderRadius: 3,
  },
  scrollContent: {
    paddingHorizontal: 4,
    gap: 12,
  },
  photoSlot: {
    width: 120,
    height: 160,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: Colors.light.surface,
  },
  emptySlot: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: "dashed",
    borderRadius: 12,
    padding: 12,
  },
  slotIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  slotLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.light.text,
    textAlign: "center",
  },
  requiredBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: Colors.light.primary + "20",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  requiredText: {
    fontSize: 10,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  photoImage: {
    width: "100%",
    height: "100%",
  },
  photoOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "space-between",
    padding: 8,
  },
  photoHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  qualityIndicator: {
    backgroundColor: "rgba(255,255,255,0.9)",
    borderRadius: 12,
    padding: 4,
  },
  removeButton: {
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  photoFooter: {
    gap: 4,
  },
  photoLabel: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
    textShadowColor: "rgba(0,0,0,0.5)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  retakeButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.warning,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    alignSelf: "flex-start",
  },
  retakeText: {
    color: "white",
    fontSize: 11,
    fontWeight: "500",
  },
  addMoreSlot: {
    borderWidth: 2,
    borderColor: Colors.light.primary + "30",
    borderStyle: "solid",
    backgroundColor: Colors.light.primary + "10",
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
  },
  addMoreText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.light.primary,
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: "center",
    marginTop: 12,
    fontStyle: "italic",
  },
});