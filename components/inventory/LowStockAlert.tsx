import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { AlertTriangle, Package } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth-store';

interface LowStockProduct {
  product_id: string;
  brand: string;
  name: string;
  category: string;
  stock_ml: number;
  minimum_stock_ml: number;
  percentage_remaining: number;
  color_code?: string;
}

interface LowStockAlertProps {
  onProductPress?: (productId: string) => void;
  compact?: boolean;
}

export function LowStockAlert({ onProductPress, compact = false }: LowStockAlertProps) {
  const [lowStockProducts, setLowStockProducts] = useState<LowStockProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuthStore();

  useEffect(() => {
    if (user?.salonId) {
      loadLowStockProducts();
    }
  }, [user?.salonId]);

  const loadLowStockProducts = async () => {
    if (!user?.salonId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .rpc('get_low_stock_products', { p_salon_id: user.salonId });

      if (error) throw error;

      setLowStockProducts(data || []);
    } catch (error) {
      console.error('Error loading low stock products:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading || lowStockProducts.length === 0) {
    return null;
  }

  const getSeverityColor = (percentage: number) => {
    if (percentage <= 10) return Colors.light.error;
    if (percentage <= 25) return Colors.light.warning;
    return Colors.light.accent;
  };

  const renderProduct = ({ item }: { item: LowStockProduct }) => {
    const severityColor = getSeverityColor(item.percentage_remaining);
    
    return (
      <TouchableOpacity
        style={[styles.productCard, { borderLeftColor: severityColor }]}
        onPress={() => onProductPress?.(item.product_id)}
      >
        <View style={styles.productHeader}>
          <View style={styles.productInfo}>
            <Text style={styles.productName}>
              {item.brand} {item.name}
              {item.color_code && <Text style={styles.colorCode}> ({item.color_code})</Text>}
            </Text>
            <Text style={styles.productCategory}>{item.category}</Text>
          </View>
          <View style={[styles.percentageBadge, { backgroundColor: severityColor + '20' }]}>
            <Text style={[styles.percentageText, { color: severityColor }]}>
              {Math.round(item.percentage_remaining)}%
            </Text>
          </View>
        </View>
        <View style={styles.stockInfo}>
          <Text style={styles.stockText}>
            Stock: {item.stock_ml}ml / Mínimo: {item.minimum_stock_ml}ml
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (compact) {
    return (
      <TouchableOpacity 
        style={styles.compactAlert}
        onPress={() => onProductPress?.('inventory')}
      >
        <View style={styles.compactHeader}>
          <AlertTriangle size={16} color={Colors.light.warning} />
          <Text style={styles.compactTitle}>
            {lowStockProducts.length} producto{lowStockProducts.length !== 1 ? 's' : ''} con stock bajo
          </Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Package size={20} color={Colors.light.warning} />
        <Text style={styles.title}>Productos con Stock Bajo</Text>
      </View>
      
      <FlatList
        data={lowStockProducts}
        keyExtractor={(item) => item.product_id}
        renderItem={renderProduct}
        scrollEnabled={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  productCard: {
    backgroundColor: 'white',
    borderRadius: radius.md,
    padding: spacing.md,
    borderLeftWidth: 4,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  productInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  productName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  colorCode: {
    fontWeight: typography.weights.normal,
    color: Colors.light.gray,
  },
  productCategory: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginTop: 2,
  },
  percentageBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
  },
  percentageText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold,
  },
  stockInfo: {
    marginTop: spacing.xs,
  },
  stockText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  separator: {
    height: spacing.sm,
  },
  compactAlert: {
    backgroundColor: Colors.light.warning + '15',
    borderRadius: radius.md,
    padding: spacing.sm,
    marginBottom: spacing.sm,
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  compactTitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.warning,
    fontWeight: typography.weights.medium,
    marginLeft: spacing.xs,
  },
});