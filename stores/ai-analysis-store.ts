/**
 * AI Analysis Store - Optimizado v2
 * 
 * Optimizaciones:
 * - performImageAnalysis unificada (-200 líneas)
 * - Logging condicional (sin logs en producción)
 * - ImageProcessor para compresión centralizada
 * - Retry con exponential backoff mejorado
 * - Reducido de 646 a ~390 líneas
 */

import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase } from "@/lib/supabase";
import { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import { ImageProcessor } from '@/utils/image-processor';

// ============= INTERFACES =============
interface ZoneAnalysisResult {
  zone: string;
  depthLevel: number;
  tone: string;
  undertone: string;
  state: string;
  grayPercentage?: number;
  porosity: string;
  elasticity: string;
  resistance: string;
  damage: string;
  unwantedTone?: string;
  confidence: number;
  grayType?: string;
  grayPattern?: string;
  pigmentAccumulation?: string;
  cuticleState?: string;
  demarkationBands?: { location: number; contrast: string }[];
}

interface AIAnalysisResult {
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallUndertone: string;
  averageDepthLevel: number;
  zoneAnalysis: {
    roots: ZoneAnalysisResult;
    mids: ZoneAnalysisResult;
    ends: ZoneAnalysisResult;
  };
  detectedChemicalProcess?: string;
  estimatedLastProcessDate?: string;
  detectedHomeRemedies?: boolean;
  detectedRisks?: {
    metalSalts: { detected: boolean; confidence: number; signs: string[] };
    henna: { detected: boolean; confidence: number; signs: string[] };
    extremeDamage: { detected: boolean; zones: string[] };
  };
  serviceComplexity?: 'simple' | 'medium' | 'complex';
  estimatedTime?: number;
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
  analysisTimestamp: number;
}

interface DesiredPhotoAnalysis {
  photoId: string;
  detectedLevel: number;
  detectedTone: string;
  detectedTechnique: string;
  detectedTones: string[];
  viabilityScore: number;
  estimatedSessions: number;
  requiredProcesses: string[];
  confidence: number;
  warnings?: string[];
}

interface AIAnalysisSettings {
  autoFaceBlur: boolean;
  imageQualityThreshold: number;
  privacyMode: boolean;
  saveAnalysisHistory: boolean;
}

interface AIAnalysisState {
  isAnalyzing: boolean;
  analysisResult: AIAnalysisResult | null;
  analysisHistory: AIAnalysisResult[];
  isAnalyzingDesiredPhoto: boolean;
  desiredPhotoAnalyses: Record<string, DesiredPhotoAnalysis>;
  settings: AIAnalysisSettings;
  privacyMode: boolean;
  
  analyzeImage: (imageUri: string) => Promise<void>;
  analyzeDesiredPhoto: (photoId: string, imageUri: string, currentLevel: number) => Promise<DesiredPhotoAnalysis | null>;
  clearAnalysis: () => void;
  updateSettings: (settings: Partial<AIAnalysisSettings>) => Promise<void>;
  setPrivacyMode: (enabled: boolean) => void;
  getAnalysisHistory: () => AIAnalysisResult[];
  clearAnalysisHistory: () => Promise<void>;
}

// ============= CONFIGURACIÓN =============
const RETRY_CONFIG = {
  maxAttempts: 3,
  timeout: 30000,
  backoffDelay: 1500
};

// ============= FUNCIÓN COMPARTIDA PRINCIPAL =============
async function performImageAnalysis(
  imageUri: string,
  task: 'diagnose_image' | 'analyze_desired_look',
  additionalPayload: any = {}
): Promise<any> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), RETRY_CONFIG.timeout);

  try {
    // 1. Validar y comprimir imagen
    const validation = await ImageProcessor.validateQuality(imageUri);
    if (!validation.valid) {
      const errors = validation.issues
        .filter(i => i.severity === 'error')
        .map(i => i.message)
        .join('. ');
      throw new Error(errors || 'Imagen no válida');
    }
    
    const purpose = task === 'diagnose_image' ? 'diagnosis' : 'desired';
    const imageBase64 = await ImageProcessor.compressForAI(imageUri, purpose);

    // 2. Llamar Edge Function con reintentos
    let lastError: any;
    for (let attempt = 1; attempt <= RETRY_CONFIG.maxAttempts; attempt++) {
      try {
        const { data, error } = await supabase.functions.invoke('salonier-assistant', {
          body: { task, payload: { imageBase64, ...additionalPayload } },
          signal: controller.signal
        });

        if (error) throw new Error(await handleEdgeFunctionError(error));
        if (!data) throw new Error('Sin respuesta del servidor');
        if (data.success === true && data.data === null) throw new Error('Respuesta vacía de OpenAI');
        
        if (!data.success) {
          if (data.error === 'Invalid response from OpenAI' && attempt < RETRY_CONFIG.maxAttempts) {
            await new Promise(r => setTimeout(r, RETRY_CONFIG.backoffDelay * attempt));
            continue;
          }
          throw new Error(data.error || 'Error desconocido');
        }

        if (!data.data) throw new Error('Respuesta incompleta');
        
        clearTimeout(timeoutId);
        return data.data;

      } catch (error: any) {
        lastError = error;
        if (error.name === 'AbortError') throw new Error('TIMEOUT_ERROR');
        
        if (attempt < RETRY_CONFIG.maxAttempts && !error.message.includes('TIMEOUT_ERROR')) {
          const delay = RETRY_CONFIG.backoffDelay * Math.pow(2, attempt - 1);
          await new Promise(r => setTimeout(r, delay));
        } else {
          throw error;
        }
      }
    }
    throw lastError || new Error('Máximos reintentos alcanzados');
  } catch (error: any) {
    clearTimeout(timeoutId);
    throw error;
  } finally {
    clearTimeout(timeoutId);
  }
}

// ============= FUNCIONES ESPECÍFICAS =============
async function performAIAnalysis(imageUri: string): Promise<AIAnalysisResult> {
  const result = await performImageAnalysis(imageUri, 'diagnose_image', {});
  
  if (!result.hairThickness || !result.hairDensity || !result.zoneAnalysis) {
    throw new Error('Análisis incompleto. Intenta con otra foto más clara.');
  }
  
  return { ...result, analysisTimestamp: Date.now() };
}

async function analyzeDesiredColorPhoto(
  photoId: string, 
  imageUri: string, 
  currentLevel: number
): Promise<DesiredPhotoAnalysis> {
  const result = await performImageAnalysis(imageUri, 'analyze_desired_look', { currentLevel });
  return { photoId, ...result };
}

// ============= MANEJO DE ERRORES =============
async function handleEdgeFunctionError(error: any): Promise<string> {
  logger.error('Edge Function Error:', error);
  
  if (error instanceof FunctionsHttpError) {
    try {
      const errorData = await error.context.json();
      logger.error('Function returned error:', errorData);
      return errorData.error || errorData.message || 'Error en el servidor';
    } catch (e) {
      logger.error('Could not parse error response:', e);
      return 'Error al procesar la respuesta del servidor';
    }
  } else if (error instanceof FunctionsRelayError) {
    return 'Error de conexión con el servidor';
  } else if (error instanceof FunctionsFetchError) {
    return 'Error al conectar con el servidor';
  } else {
    return error.message || 'Error desconocido';
  }
}

// ============= ZUSTAND STORE =============
export const useAIAnalysisStore = create<AIAnalysisState>((set, get) => ({
  isAnalyzing: false,
  analysisResult: null,
  analysisHistory: [],
  isAnalyzingDesiredPhoto: false,
  desiredPhotoAnalyses: {},
  privacyMode: true,
  settings: {
    autoFaceBlur: true,
    imageQualityThreshold: 60,
    privacyMode: true,
    saveAnalysisHistory: false,
  },

  analyzeImage: async (imageUri: string) => {
    set({ isAnalyzing: true, analysisResult: null });
    
    try {
      const result = await performAIAnalysis(imageUri);
      logger.info("AI Analysis completed successfully");
      
      set(state => ({
        isAnalyzing: false,
        analysisResult: result,
        analysisHistory: state.settings.saveAnalysisHistory 
          ? [...state.analysisHistory, result]
          : state.analysisHistory
      }));
      
      // Guardar historial si está habilitado
      const { settings, analysisHistory } = get();
      if (settings.saveAnalysisHistory) {
        try {
          await AsyncStorage.setItem(
            'salonier-analysis-history', 
            JSON.stringify(analysisHistory)
          );
        } catch (error) {
          logger.error('Error saving analysis history:', error);
        }
      }
      
    } catch (error: any) {
      set({ isAnalyzing: false });
      logger.error('Error in analyzeImage:', error);
      
      // Mensajes de error user-friendly
      if (error.message === 'TIMEOUT_ERROR') {
        throw new Error('El análisis tardó demasiado. Por favor, intenta de nuevo.');
      } else if (error.message.includes('servidor')) {
        throw new Error('No se pudo conectar con el servidor. Verifica tu conexión.');
      } else {
        throw new Error('Error al procesar la imagen. Por favor, intenta con otra foto.');
      }
    }
  },

  analyzeDesiredPhoto: async (photoId: string, imageUri: string, currentLevel: number) => {
    logger.info('Starting desired photo analysis', { photoId, currentLevel });
    set({ isAnalyzingDesiredPhoto: true });
    
    try {
      const analysis = await analyzeDesiredColorPhoto(photoId, imageUri, currentLevel);
      
      set(state => ({
        isAnalyzingDesiredPhoto: false,
        desiredPhotoAnalyses: {
          ...state.desiredPhotoAnalyses,
          [photoId]: analysis
        }
      }));
      
      return analysis;
    } catch (error: any) {
      set({ isAnalyzingDesiredPhoto: false });
      logger.error('Error analyzing desired photo:', error);
      
      if (error.message === 'TIMEOUT_ERROR') {
        throw new Error('TIMEOUT_ERROR');
      }
      
      return null;
    }
  },

  clearAnalysis: () => {
    set({ analysisResult: null, desiredPhotoAnalyses: {} });
  },

  updateSettings: async (newSettings: Partial<AIAnalysisSettings>) => {
    const updatedSettings = { ...get().settings, ...newSettings };
    set({ settings: updatedSettings });
    
    try {
      await AsyncStorage.setItem(
        'salonier-ai-settings', 
        JSON.stringify(updatedSettings)
      );
    } catch (error) {
      logger.error('Error saving AI settings:', error);
    }
  },

  setPrivacyMode: (enabled: boolean) => {
    set({ privacyMode: enabled });
  },

  getAnalysisHistory: () => {
    return get().analysisHistory;
  },

  clearAnalysisHistory: async () => {
    set({ analysisHistory: [] });
    try {
      await AsyncStorage.removeItem('salonier-analysis-history');
    } catch (error) {
      logger.error('Error clearing analysis history:', error);
    }
  }
}));

// ============= INICIALIZACIÓN =============
const initializeAIAnalysisStore = async () => {
  try {
    const savedSettings = await AsyncStorage.getItem('salonier-ai-settings');
    const savedHistory = await AsyncStorage.getItem('salonier-analysis-history');
    
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      useAIAnalysisStore.getState().updateSettings(settings);
    }
    
    if (savedHistory) {
      const history = JSON.parse(savedHistory);
      useAIAnalysisStore.setState({ analysisHistory: history });
    }
  } catch (error) {
    logger.error('Error initializing AI analysis store:', error);
  }
};

initializeAIAnalysisStore();