import { create } from 'zustand';
import { createJSONStorage, persist, subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PricingConfiguration, SalonConfiguration } from '@/types/inventory';
import { RegionalConfig, CountryCode } from '@/types/regional';
import { getCountryByCode } from '@/constants/reference-data/countries-data';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from './auth-store';
import { logger } from '@/utils/logger';

interface SalonConfigStore {
  configuration: SalonConfiguration;
  regionalConfig: RegionalConfig | null;
  isInitialized: boolean;
  skipSafetyVerification: boolean;
  hasCompletedOnboarding: boolean;
  
  // Actions
  updateBusinessName: (name: string) => Promise<void>;
  updateInventoryControlLevel: (level: 'solo-formulas' | 'smart-cost' | 'control-total') => Promise<void>;
  updatePricing: (pricing: Partial<PricingConfiguration>) => Promise<void>;
  updateNotifications: (notifications: Partial<SalonConfiguration['notifications']>) => Promise<void>;
  setAutoConsumption: (enabled: boolean) => Promise<void>;
  setRequireStockValidation: (enabled: boolean) => Promise<void>;
  updateCountry: (countryCode: CountryCode) => Promise<void>;
  updateMeasurementSystem: (system: 'metric' | 'imperial') => Promise<void>;
  updateLanguage: (language: string) => Promise<void>;
  setSkipSafetyVerification: (skip: boolean) => Promise<void>;
  setHasCompletedOnboarding: (completed: boolean) => Promise<void>;
  initializeDefaults: () => void;
  resetConfiguration: () => void;
  syncWithSupabase: () => Promise<void>;
  updateSalonSettings: () => Promise<void>;
  
  // Helpers
  formatCurrency: (amount: number) => string;
  applyMarkup: (cost: number) => number;
  roundPrice: (price: number) => number;
  formatWeight: (value: number, includeUnit?: boolean) => string;
  formatVolume: (value: number, includeUnit?: boolean) => string;
  convertVolume: (value: number, from: 'ml' | 'fl oz', to: 'ml' | 'fl oz') => number;
  convertWeight: (value: number, from: 'g' | 'oz', to: 'g' | 'oz') => number;
  getUnitLabel: (type: 'volume' | 'weight') => string;
  getTerminology: (type: 'developer' | 'color') => string;
}

const defaultPricing: PricingConfiguration = {
  defaultMarkupPercentage: 300, // 300% markup (4x cost)
  roundingPolicy: 'nearest',
  roundingIncrement: 0.5,
  minimumServicePrice: 20,
  includeTaxInPrice: true,
  taxPercentage: 21, // IVA España
  currency: 'EUR',
  currencySymbol: '€',
  lastUpdated: new Date().toISOString(),
};

const defaultConfiguration: SalonConfiguration = {
  businessName: 'Mi Salón',
  inventoryControlLevel: 'smart-cost',
  pricing: defaultPricing,
  notifications: {
    lowStockAlerts: true,
    expirationAlerts: true,
    restockReminders: true,
  },
  autoConsumption: false,
  requireStockValidation: true,
  countryCode: 'ES',
  measurementSystem: 'metric',
  language: 'es',
};

// Logger contextual para salon-config-store
const configLogger = logger.withContext('salon-config');

export const useSalonConfigStore = create<SalonConfigStore>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
      configuration: defaultConfiguration,
      regionalConfig: getCountryByCode('ES')?.config || {
        countryCode: 'ES',
        countryName: 'España',
        region: 'Europe',
        measurementSystem: 'metric',
        currency: 'EUR',
        currencySymbol: '€',
        language: 'es',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        decimalSeparator: ',',
        thousandsSeparator: '.',
        volumeUnit: 'ml',
        weightUnit: 'g',
        developerTerminology: 'oxidante',
        colorTerminology: 'tinte',
        requiresAllergyTest: false,
        maxDeveloperVolume: 40,
      },
      isInitialized: false,
      skipSafetyVerification: false,
      hasCompletedOnboarding: false,

      updateBusinessName: async (name) => {
        configLogger.info('Updating business name', { name });

        set((state) => ({
          configuration: { ...state.configuration, businessName: name },
        }));

        // Update in Supabase de forma asíncrona
        const user = useAuthStore.getState().user;
        if (user?.salonId) {
          supabase
            .from('salons')
            .update({ name })
            .eq('id', user.salonId)
            .then(({ error }) => {
              if (error) {
                configLogger.error('Error updating salon name:', error);
              } else {
                configLogger.info('Salon name updated successfully');
              }
            });
        }
      },

      updateInventoryControlLevel: async (level) => {
        configLogger.info('Updating inventory control level', { level });

        set((state) => ({
          configuration: { ...state.configuration, inventoryControlLevel: level },
        }));

        // Update in Supabase settings de forma asíncrona
        get().updateSalonSettings().catch(error => {
          configLogger.error('Error updating salon settings:', error);
        });
      },

      updatePricing: async (pricing) => {
        configLogger.info('Updating pricing configuration', { pricing });

        set((state) => ({
          configuration: {
            ...state.configuration,
            pricing: {
              ...state.configuration.pricing,
              ...pricing,
              lastUpdated: new Date().toISOString(),
            },
          },
        }));

        // Update in Supabase settings de forma asíncrona
        get().updateSalonSettings().catch(error => {
          configLogger.error('Error updating salon settings:', error);
        });
      },

      updateNotifications: async (notifications) => {
        set((state) => ({
          configuration: {
            ...state.configuration,
            notifications: {
              ...state.configuration.notifications,
              ...notifications,
            },
          },
        }));

        // Update in Supabase settings
        await get().updateSalonSettings();
      },

      setAutoConsumption: async (enabled) => {
        configLogger.info('Setting auto consumption', { enabled });
        set((state) => ({
          configuration: { ...state.configuration, autoConsumption: enabled },
        }));

        // Actualizar de forma asíncrona
        get().updateSalonSettings().catch(error => {
          configLogger.error('Error updating salon settings:', error);
        });
      },

      setRequireStockValidation: async (enabled) => {
        configLogger.info('Setting require stock validation', { enabled });
        set((state) => ({
          configuration: { ...state.configuration, requireStockValidation: enabled },
        }));

        // Actualizar de forma asíncrona
        get().updateSalonSettings().catch(error => {
          configLogger.error('Error updating salon settings:', error);
        });
      },

      setSkipSafetyVerification: async (skip) => {
        configLogger.info('Setting skip safety verification', { skip });
        set(() => ({
          skipSafetyVerification: skip,
        }));

        // Actualizar de forma asíncrona
        get().updateSalonSettings().catch(error => {
          configLogger.error('Error updating salon settings:', error);
        });
      },

      setHasCompletedOnboarding: async (completed) => {
        configLogger.info('Setting onboarding completion status', { completed });
        set(() => ({
          hasCompletedOnboarding: completed,
        }));

        // Actualizar de forma asíncrona
        get().updateSalonSettings().catch(error => {
          configLogger.error('Error updating salon settings:', error);
        });
      },

      initializeDefaults: () =>
        set(() => ({
          configuration: defaultConfiguration,
          isInitialized: true,
        })),

      updateCountry: async (countryCode) => {
        const countryInfo = getCountryByCode(countryCode);
        if (countryInfo) {
          set((state) => ({
            configuration: {
              ...state.configuration,
              countryCode,
              measurementSystem: countryInfo.config.measurementSystem,
              language: countryInfo.config.language,
            },
            regionalConfig: countryInfo.config,
          }));
          
          // Update pricing with new currency
          await get().updatePricing({
            currency: countryInfo.config.currency,
            currencySymbol: countryInfo.config.currencySymbol,
          });
        }
      },

      updateMeasurementSystem: async (system) => {
        set((state) => ({
          configuration: { ...state.configuration, measurementSystem: system },
          regionalConfig: state.regionalConfig ? {
            ...state.regionalConfig,
            measurementSystem: system,
            volumeUnit: system === 'metric' ? 'ml' : 'fl oz',
            weightUnit: system === 'metric' ? 'g' : 'oz',
          } : null,
        }));

        await get().updateSalonSettings();
      },

      updateLanguage: async (language) => {
        set((state) => ({
          configuration: { ...state.configuration, language },
          regionalConfig: state.regionalConfig ? {
            ...state.regionalConfig,
            language: language as any,
          } : null,
        }));

        await get().updateSalonSettings();
      },

      resetConfiguration: () =>
        set(() => ({
          configuration: defaultConfiguration,
          regionalConfig: getCountryByCode('ES')?.config || null,
          isInitialized: false,
          skipSafetyVerification: false,
          hasCompletedOnboarding: false,
        })),

      syncWithSupabase: async () => {
        const user = useAuthStore.getState().user;
        if (!user?.salonId) return;

        try {
          const { data: salon, error } = await supabase
            .from('salons')
            .select('*')
            .eq('id', user.salonId)
            .single();

          if (error) throw error;

          if (salon && salon.settings) {
            const settings = salon.settings as any;
            
            // Update configuration from stored settings
            set((state) => ({
              configuration: {
                ...state.configuration,
                businessName: salon.name,
                inventoryControlLevel: settings.inventoryControlLevel || state.configuration.inventoryControlLevel,
                pricing: settings.pricing || state.configuration.pricing,
                notifications: settings.notifications || state.configuration.notifications,
                autoConsumption: settings.autoConsumption ?? state.configuration.autoConsumption,
                requireStockValidation: settings.requireStockValidation ?? state.configuration.requireStockValidation,
                countryCode: settings.countryCode || state.configuration.countryCode,
                measurementSystem: settings.measurementSystem || state.configuration.measurementSystem,
                language: settings.language || state.configuration.language,
              },
              skipSafetyVerification: settings.skipSafetyVerification ?? false,
              hasCompletedOnboarding: settings.hasCompletedOnboarding ?? false,
            }));

            // Update regional config if country changed
            const countryCode = settings.countryCode || 'ES';
            const countryInfo = getCountryByCode(countryCode as CountryCode);
            if (countryInfo) {
              set({ regionalConfig: countryInfo.config });
            }
          }
        } catch (error) {
          console.error('Error syncing salon config:', error);
          throw error;
        }
      },

      updateSalonSettings: async () => {
        configLogger.startTimer('updateSalonSettings');
        const user = useAuthStore.getState().user;
        if (!user?.salonId) {
          configLogger.warn('No salon ID found, skipping settings update');
          return;
        }

        const state = get();
        const settings = {
          inventoryControlLevel: state.configuration.inventoryControlLevel,
          pricing: state.configuration.pricing,
          notifications: state.configuration.notifications,
          autoConsumption: state.configuration.autoConsumption,
          requireStockValidation: state.configuration.requireStockValidation,
          countryCode: state.configuration.countryCode,
          measurementSystem: state.configuration.measurementSystem,
          language: state.configuration.language,
          skipSafetyVerification: state.skipSafetyVerification,
          hasCompletedOnboarding: state.hasCompletedOnboarding,
        };

        try {
          const { error } = await supabase
            .from('salons')
            .update({ settings })
            .eq('id', user.salonId);

          if (error) throw error;
          configLogger.info('Salon settings updated successfully');
        } catch (error) {
          configLogger.error('Error updating salon settings:', error);
          throw error;
        } finally {
          configLogger.endTimer('updateSalonSettings');
        }
      },

      formatCurrency: (amount) => {
        const { currency, currencySymbol, includeTaxInPrice, taxPercentage } = get().configuration.pricing;
        
        let finalAmount = amount;
        if (!includeTaxInPrice) {
          finalAmount = amount * (1 + taxPercentage / 100);
        }
        
        const rounded = get().roundPrice(finalAmount);
        
        return new Intl.NumberFormat('es-ES', {
          style: 'currency',
          currency: currency,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(rounded).replace(currency, currencySymbol);
      },

      applyMarkup: (cost) => {
        const { defaultMarkupPercentage, minimumServicePrice } = get().configuration.pricing;
        const markedUpPrice = cost * (1 + defaultMarkupPercentage / 100);
        return Math.max(markedUpPrice, minimumServicePrice);
      },

      roundPrice: (price) => {
        const { roundingPolicy, roundingIncrement } = get().configuration.pricing;
        
        if (roundingPolicy === 'none') return price;
        
        const factor = 1 / roundingIncrement;
        
        switch (roundingPolicy) {
          case 'up':
            return Math.ceil(price * factor) / factor;
          case 'down':
            return Math.floor(price * factor) / factor;
          case 'nearest':
          default:
            return Math.round(price * factor) / factor;
        }
      },

      formatWeight: (value, includeUnit = true) => {
        const regional = get().regionalConfig;
        if (!regional) return `${value}${includeUnit ? 'g' : ''}`;
        
        let displayValue = value;
        let unit = regional.weightUnit;
        
        if (regional.weightUnit === 'oz' && regional.measurementSystem === 'imperial') {
          // Convert grams to ounces (1g = 0.035274oz)
          displayValue = value * 0.035274;
          unit = 'oz';
        }
        
        const formatted = displayValue.toFixed(1).replace('.', regional.decimalSeparator);
        return includeUnit ? `${formatted} ${unit}` : formatted;
      },

      formatVolume: (value, includeUnit = true) => {
        const regional = get().regionalConfig;
        if (!regional) return `${value}${includeUnit ? 'ml' : ''}`;
        
        let displayValue = value;
        let unit = regional.volumeUnit;
        
        if (regional.volumeUnit === 'fl oz' && regional.measurementSystem === 'imperial') {
          // Convert ml to fl oz (1ml = 0.033814 fl oz)
          displayValue = value * 0.033814;
          unit = 'fl oz';
        }
        
        const formatted = displayValue.toFixed(1).replace('.', regional.decimalSeparator);
        return includeUnit ? `${formatted} ${unit}` : formatted;
      },

      convertVolume: (value, from, to) => {
        if (from === to) return value;
        
        const ML_TO_FL_OZ = 0.033814;
        const FL_OZ_TO_ML = 29.5735;
        
        if (from === 'ml' && to === 'fl oz') {
          return value * ML_TO_FL_OZ;
        } else if (from === 'fl oz' && to === 'ml') {
          return value * FL_OZ_TO_ML;
        }
        
        return value;
      },

      convertWeight: (value, from, to) => {
        if (from === to) return value;
        
        const G_TO_OZ = 0.035274;
        const OZ_TO_G = 28.3495;
        
        if (from === 'g' && to === 'oz') {
          return value * G_TO_OZ;
        } else if (from === 'oz' && to === 'g') {
          return value * OZ_TO_G;
        }
        
        return value;
      },

      getUnitLabel: (type) => {
        const regional = get().regionalConfig;
        if (!regional) return type === 'volume' ? 'ml' : 'g';
        
        if (type === 'volume') {
          return regional.volumeUnit;
        } else {
          return regional.weightUnit;
        }
      },

      getTerminology: (type) => {
        const regional = get().regionalConfig;
        if (!regional) return type === 'developer' ? 'oxidante' : 'tinte';
        
        if (type === 'developer') {
          return regional.developerTerminology;
        } else {
          return regional.colorTerminology;
        }
      },
    }),
    {
      name: 'salon-config-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        if (state && !state.isInitialized) {
          state.isInitialized = true;
          // Initialize regional config if not set
          if (!state.regionalConfig) {
            const countryCode = state.configuration.countryCode || 'ES';
            const countryInfo = getCountryByCode(countryCode as CountryCode);
            if (countryInfo) {
              state.regionalConfig = countryInfo.config;
            } else {
              // Fallback to Spanish config if country not found
              state.regionalConfig = {
                countryCode: 'ES',
                countryName: 'España',
                region: 'Europe',
                measurementSystem: 'metric',
                currency: 'EUR',
                currencySymbol: '€',
                language: 'es',
                dateFormat: 'DD/MM/YYYY',
                timeFormat: '24h',
                decimalSeparator: ',',
                thousandsSeparator: '.',
                volumeUnit: 'ml',
                weightUnit: 'g',
                developerTerminology: 'oxidante',
                colorTerminology: 'tinte',
                requiresAllergyTest: false,
                maxDeveloperVolume: 40,
              };
            }
          }
        }
      },
    }
  )
);

// ============= SELECTORES OPTIMIZADOS =============
// Estos selectores ayudan a prevenir re-renders innecesarios

export const useSalonConfiguration = () => useSalonConfigStore(state => state.configuration);
export const useRegionalConfig = () => useSalonConfigStore(state => state.regionalConfig);
export const usePricingConfig = () => useSalonConfigStore(state => state.configuration.pricing);
export const useBusinessName = () => useSalonConfigStore(state => state.configuration.businessName);
export const useInventoryControlLevel = () => useSalonConfigStore(state => state.configuration.inventoryControlLevel);
export const useMeasurementSystem = () => useSalonConfigStore(state => state.configuration.measurementSystem);
export const useCountryCode = () => useSalonConfigStore(state => state.configuration.countryCode);
export const useNotificationSettings = () => useSalonConfigStore(state => state.configuration.notifications);
export const useOnboardingStatus = () => useSalonConfigStore(state => state.hasCompletedOnboarding);

// Selectores para helpers (memoizados)
export const useCurrencyFormatter = () => useSalonConfigStore(state => state.formatCurrency);
export const usePriceCalculator = () => useSalonConfigStore(state => ({
  applyMarkup: state.applyMarkup,
  roundPrice: state.roundPrice
}));
export const useUnitConverters = () => useSalonConfigStore(state => ({
  convertVolume: state.convertVolume,
  convertWeight: state.convertWeight,
  formatVolume: state.formatVolume,
  formatWeight: state.formatWeight,
  getUnitLabel: state.getUnitLabel
}));
export const useTerminology = () => useSalonConfigStore(state => state.getTerminology);