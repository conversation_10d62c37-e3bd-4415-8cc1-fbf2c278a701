import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase, getCurrentSalonId } from "@/lib/supabase";
import { useSyncQueueStore, generateLocalId, isLocalId } from "./sync-queue-store";
import { Database } from "@/types/database";
import { useAuthStore } from "./auth-store";
import { logger } from "@/utils/logger";

// Tipos locales (mantenemos los mismos)
export interface ClientAllergy {
  substance: string;
  severity: 'leve' | 'moderada' | 'severa';
  dateDetected: string;
  notes?: string;
}

export interface ClientPreference {
  category: 'tono' | 'marca' | 'tecnica' | 'tiempo';
  value: string;
  priority: 'alta' | 'media' | 'baja';
}

export interface HairEvolution {
  date: string;
  level: string;
  porosity: string;
  damage: string;
  resistance: string;
  notes: string;
}

export interface PreviousFormula {
  id: string;
  date: string;
  formula: string;
  brand: string;
  line: string;
  result: 'excelente' | 'bueno' | 'regular' | 'malo';
  satisfaction: number;
  notes: string;
  processingTime: number;
  oxidantVolume: string;
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface PatchTest {
  id: string;
  date: string;
  products: string[];
  result: 'negativo' | 'positivo' | 'pendiente';
  notes?: string;
  reminderSent: boolean;
}

export interface ConsentRecord {
  id: string;
  date: string;
  consentItems: Array<{
    id: string;
    title: string;
    description: string;
    accepted: boolean;
  }>;
  signature: string;
  safetyChecklist: Array<{
    id: string;
    title: string;
    checked: boolean;
  }>;
  ipAddress: string;
  userAgent: string;
  skipSafetyVerification?: boolean;
  _syncStatus?: 'synced' | 'pending' | 'error';
  _localId?: string;
}

export interface ClientHistoryProfile {
  clientId: string;
  allergies: ClientAllergy[];
  preferences: ClientPreference[];
  hairEvolution: HairEvolution[];
  previousFormulas: PreviousFormula[];
  patchTests: PatchTest[];
  consentRecords: ConsentRecord[];
  riskLevel: 'bajo' | 'medio' | 'alto';
  lastAnalysisDate?: string;
  totalServices: number;
  averageSatisfaction: number;
  preferredBrands: string[];
  avoidedIngredients: string[];
  specialNotes: string[];
}

type SupabaseService = Database['public']['Tables']['services']['Row'];
type SupabaseServiceInsert = Database['public']['Tables']['services']['Insert'];
type SupabaseFormula = Database['public']['Tables']['formulas']['Row'];
type SupabaseFormulaInsert = Database['public']['Tables']['formulas']['Insert'];
type SupabaseConsent = Database['public']['Tables']['client_consents']['Row'];
type SupabaseConsentInsert = Database['public']['Tables']['client_consents']['Insert'];

interface ClientHistoryState {
  clientProfiles: Record<string, ClientHistoryProfile>;
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  loadClientHistory: (clientId: string) => Promise<void>;
  getClientProfile: (clientId: string) => ClientHistoryProfile | null;
  updateClientProfile: (clientId: string, profile: Partial<ClientHistoryProfile>) => void;
  addAllergy: (clientId: string, allergy: ClientAllergy) => void;
  addPreference: (clientId: string, preference: ClientPreference) => void;
  addHairEvolution: (clientId: string, evolution: HairEvolution) => void;
  addPreviousFormula: (clientId: string, formula: PreviousFormula) => Promise<void>;
  addPatchTest: (clientId: string, test: PatchTest) => void;
  addConsentRecord: (clientId: string, consent: ConsentRecord) => Promise<void>;
  getRecommendationsForClient: (clientId: string) => string[];
  getWarningsForClient: (clientId: string) => string[];
  calculateRiskLevel: (clientId: string) => 'bajo' | 'medio' | 'alto';
  getCompatibleFormulas: (clientId: string) => PreviousFormula[];
  getLastPatchTest: (clientId: string) => PatchTest | null;
  getLastConsent: (clientId: string) => ConsentRecord | null;
  initializeClientProfile: (clientId: string) => void;
  syncWithSupabase: () => Promise<void>;
}

const historyLogger = logger.withContext('ClientHistoryStore');

// Helper functions for conversion
function convertSupabaseServiceToFormula(service: SupabaseService, formula?: SupabaseFormula): PreviousFormula | null {
  if (!formula || !formula.formula_text) return null;
  
  return {
    id: formula.id,
    date: service.service_date,
    formula: formula.formula_text,
    brand: formula.brand || '',
    line: formula.line || '',
    result: (service.satisfaction_score >= 4 ? 'excelente' : 
             service.satisfaction_score >= 3 ? 'bueno' : 
             service.satisfaction_score >= 2 ? 'regular' : 'malo') as PreviousFormula['result'],
    satisfaction: service.satisfaction_score || 3,
    notes: service.notes || '',
    processingTime: formula.processing_time || 35,
    oxidantVolume: formula.developer_volume?.toString() || '20',
    _syncStatus: 'synced',
  };
}

function convertSupabaseConsentToLocal(consent: SupabaseConsent): ConsentRecord {
  return {
    id: consent.id,
    date: consent.created_at,
    consentItems: consent.consent_data?.items || [],
    signature: consent.signature_data || '',
    safetyChecklist: consent.safety_checklist || [],
    ipAddress: consent.ip_address || '',
    userAgent: consent.user_agent || '',
    skipSafetyVerification: consent.skip_safety || false,
    _syncStatus: 'synced',
  };
}

/**
 * Sync a record to Supabase with consistent error handling
 */
async function syncRecord<T>(
  recordType: string,
  data: any,
  syncOperation: () => Promise<T>
): Promise<T | null> {
  historyLogger.startTimer(`sync${recordType}`);
  
  const { isOnline } = useSyncQueueStore.getState();
  
  if (isOnline) {
    try {
      const result = await syncOperation();
      historyLogger.endTimer(`sync${recordType}`);
      return result;
    } catch (error) {
      historyLogger.error(`Error syncing ${recordType}:`, error);
      
      // Add to sync queue
      useSyncQueueStore.getState().addToQueue({
        type: 'create',
        table: recordType.toLowerCase(),
        data,
      });
      
      return null;
    }
  } else {
    // Offline - add to queue
    useSyncQueueStore.getState().addToQueue({
      type: 'create',
      table: recordType.toLowerCase(),
      data,
    });
    
    return null;
  }
}

/**
 * Check for allergies and return warnings
 */
function checkAllergies(allergies: ClientAllergy[]): string[] {
  const warnings: string[] = [];
  
  allergies.forEach(allergy => {
    if (allergy.severity === 'severa') {
      warnings.push(`⚠️ ALERGIA SEVERA: ${allergy.substance}`);
    } else if (allergy.severity === 'moderada') {
      warnings.push(`⚠️ Alergia moderada: ${allergy.substance}`);
    }
  });
  
  return warnings;
}

/**
 * Check patch tests and return warnings
 */
function checkPatchTests(patchTests: PatchTest[]): string[] {
  const warnings: string[] = [];
  
  // Pending tests
  const pendingTests = patchTests.filter(t => t.result === 'pendiente');
  if (pendingTests.length > 0) {
    warnings.push(`⚠️ Test de parche pendiente desde ${pendingTests[0].date}`);
  }
  
  // Recent positive tests
  const recentPositiveTests = patchTests.filter(t => {
    const daysSince = Math.floor((Date.now() - new Date(t.date).getTime()) / (1000 * 60 * 60 * 24));
    return t.result === 'positivo' && daysSince <= 30;
  });
  if (recentPositiveTests.length > 0) {
    warnings.push(`⚠️ Test de parche POSITIVO reciente - NO PROCEDER`);
  }
  
  return warnings;
}

/**
 * Check consent validity
 */
function checkConsent(lastConsent: ConsentRecord | null): string[] {
  const warnings: string[] = [];
  
  if (lastConsent) {
    const daysSince = Math.floor((Date.now() - new Date(lastConsent.date).getTime()) / (1000 * 60 * 60 * 24));
    if (daysSince > 365) {
      warnings.push(`⚠️ Consentimiento vencido - Renovar antes de proceder`);
    }
  }
  
  return warnings;
}

export const useClientHistoryStore = create<ClientHistoryState>()(
  persist(
    (set, get) => ({
      clientProfiles: {},
      isLoading: false,
      isInitialized: false,

      loadClientHistory: async (clientId: string) => {
        historyLogger.startTimer('loadClientHistory');
        set({ isLoading: true });
        
        try {
          const salonId = await getCurrentSalonId();
          if (!salonId || !clientId) {
            set({ isLoading: false });
            return;
          }

          // Get client's services with formulas
          const { data: services, error: servicesError } = await supabase
            .from('services')
            .select(`
              *,
              formulas(*)
            `)
            .eq('client_id', clientId)
            .eq('salon_id', salonId)
            .order('service_date', { ascending: false });

          if (servicesError) throw servicesError;

          // Get consent records
          const { data: consents, error: consentsError } = await supabase
            .from('client_consents')
            .select('*')
            .eq('client_id', clientId)
            .eq('salon_id', salonId)
            .order('created_at', { ascending: false });

          if (consentsError) throw consentsError;

          // Process services into formulas
          const previousFormulas = services
            ?.map(service => convertSupabaseServiceToFormula(service, service.formulas?.[0]))
            .filter(Boolean) as PreviousFormula[] || [];

          // Process consents
          const consentRecords = consents?.map(convertSupabaseConsentToLocal) || [];

          // Calculate stats
          const satisfactionScores = services
            ?.map(s => s.satisfaction_score)
            .filter(s => s !== null) || [];
          
          const averageSatisfaction = satisfactionScores.length > 0
            ? satisfactionScores.reduce((a, b) => a! + b!, 0)! / satisfactionScores.length
            : 0;

          // Create or update profile
          const profile: ClientHistoryProfile = {
            clientId,
            allergies: [],
            preferences: [],
            hairEvolution: [],
            previousFormulas,
            patchTests: [],
            consentRecords,
            riskLevel: 'bajo',
            lastAnalysisDate: services?.[0]?.service_date,
            totalServices: services?.length || 0,
            averageSatisfaction,
            preferredBrands: [],
            avoidedIngredients: [],
            specialNotes: [],
          };

          set(state => ({
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: profile
            },
            isLoading: false,
            isInitialized: true
          }));
          
          historyLogger.info('Client history loaded', { 
            clientId, 
            services: services?.length || 0,
            consents: consents?.length || 0 
          });
          
          historyLogger.endTimer('loadClientHistory');
        } catch (error) {
          historyLogger.error('Error loading client history:', error);
          set({ isLoading: false });
          
          // Initialize empty profile on error
          get().initializeClientProfile(clientId);
        }
      },

      getClientProfile: (clientId: string) => {
        return get().clientProfiles[clientId] || null;
      },

      updateClientProfile: (clientId: string, profile: Partial<ClientHistoryProfile>) => {
        set(state => ({
          clientProfiles: {
            ...state.clientProfiles,
            [clientId]: {
              ...state.clientProfiles[clientId],
              ...profile
            }
          }
        }));
      },

      addAllergy: (clientId: string, allergy: ClientAllergy) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                allergies: [...profile.allergies, allergy],
                riskLevel: get().calculateRiskLevel(clientId)
              }
            }
          };
        });
        
        historyLogger.info('Allergy added', { clientId, allergy: allergy.substance });
      },

      addPreference: (clientId: string, preference: ClientPreference) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                preferences: [...profile.preferences, preference]
              }
            }
          };
        });
      },

      addHairEvolution: (clientId: string, evolution: HairEvolution) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                hairEvolution: [evolution, ...profile.hairEvolution]
              }
            }
          };
        });
      },

      addPreviousFormula: async (clientId: string, formula: PreviousFormula) => {
        const tempId = generateLocalId('formula');
        const tempFormula = { ...formula, id: tempId, _syncStatus: 'pending' as const, _localId: tempId };
        
        // Update UI immediately
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                previousFormulas: [tempFormula, ...profile.previousFormulas]
              }
            }
          };
        });
        
        // Sync to Supabase
        const salonId = await getCurrentSalonId();
        if (!salonId) return;
        
        const result = await syncRecord(
          'formula',
          {
            salon_id: salonId,
            client_id: clientId,
            formula_text: formula.formula,
            brand: formula.brand,
            line: formula.line,
            processing_time: formula.processingTime,
            developer_volume: parseInt(formula.oxidantVolume),
            _tempId: tempId,
          },
          async () => {
            const { data, error } = await supabase
              .from('formulas')
              .insert({
                salon_id: salonId,
                formula_text: formula.formula,
                brand: formula.brand,
                line: formula.line,
                processing_time: formula.processingTime,
                developer_volume: parseInt(formula.oxidantVolume),
              })
              .select()
              .single();
            
            if (error) throw error;
            return data;
          }
        );
        
        if (result) {
          // Update with real ID
          set(state => {
            const profile = state.clientProfiles[clientId];
            if (!profile) return state;

            return {
              clientProfiles: {
                ...state.clientProfiles,
                [clientId]: {
                  ...profile,
                  previousFormulas: profile.previousFormulas.map(f =>
                    f.id === tempId ? { ...f, id: result.id, _syncStatus: 'synced' } : f
                  )
                }
              }
            };
          });
        }
      },

      addPatchTest: (clientId: string, test: PatchTest) => {
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                patchTests: [test, ...profile.patchTests],
                riskLevel: get().calculateRiskLevel(clientId)
              }
            }
          };
        });
        
        historyLogger.info('Patch test added', { clientId, result: test.result });
      },

      addConsentRecord: async (clientId: string, consent: ConsentRecord) => {
        const tempId = generateLocalId('consent');
        const tempConsent = { ...consent, id: tempId, _syncStatus: 'pending' as const, _localId: tempId };
        
        // Update UI immediately
        set(state => {
          const profile = state.clientProfiles[clientId];
          if (!profile) return state;

          return {
            clientProfiles: {
              ...state.clientProfiles,
              [clientId]: {
                ...profile,
                consentRecords: [tempConsent, ...profile.consentRecords]
              }
            }
          };
        });
        
        // Sync to Supabase
        const salonId = await getCurrentSalonId();
        if (!salonId) return;
        
        const result = await syncRecord(
          'client_consents',
          {
            client_id: clientId,
            salon_id: salonId,
            consent_type: 'chemical_process',
            consent_text: 'Consentimiento informado para servicio de coloración capilar',
            consent_data: { items: consent.consentItems },
            signature_data: consent.signature,
            safety_checklist: consent.safetyChecklist,
            skip_safety: consent.skipSafetyVerification || false,
            ip_address: consent.ipAddress,
            user_agent: consent.userAgent,
            _tempId: tempId,
          },
          async () => {
            const { data, error } = await supabase
              .from('client_consents')
              .insert({
                client_id: clientId,
                salon_id: salonId,
                consent_type: 'chemical_process',
                consent_text: 'Consentimiento informado para servicio de coloración capilar',
                consent_data: { items: consent.consentItems },
                signature_data: consent.signature,
                safety_checklist: consent.safetyChecklist,
                skip_safety: consent.skipSafetyVerification || false,
                ip_address: consent.ipAddress,
                user_agent: consent.userAgent,
              })
              .select()
              .single();
            
            if (error) throw error;
            return data;
          }
        );
        
        if (result) {
          // Update with real data
          set(state => {
            const profile = state.clientProfiles[clientId];
            if (!profile) return state;

            return {
              clientProfiles: {
                ...state.clientProfiles,
                [clientId]: {
                  ...profile,
                  consentRecords: profile.consentRecords.map(c =>
                    c.id === tempId ? convertSupabaseConsentToLocal(result) : c
                  )
                }
              }
            };
          });
        }
      },

      getRecommendationsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const recommendations: string[] = [];

        // Based on successful formulas
        const successfulFormulas = profile.previousFormulas.filter(f => f.satisfaction >= 4);
        if (successfulFormulas.length > 0) {
          const mostSuccessful = successfulFormulas.sort((a, b) => b.satisfaction - a.satisfaction)[0];
          recommendations.push(`Fórmula exitosa anterior: ${mostSuccessful.brand} ${mostSuccessful.line}`);
        }

        // Based on hair evolution
        if (profile.hairEvolution.length > 1) {
          const latest = profile.hairEvolution[0];
          const previous = profile.hairEvolution[1];
          
          if (latest.damage !== previous.damage) {
            recommendations.push(`Cambio en daño capilar: de ${previous.damage} a ${latest.damage}`);
          }
        }

        // Based on preferences
        const highPriorityPrefs = profile.preferences.filter(p => p.priority === 'alta');
        highPriorityPrefs.forEach(pref => {
          recommendations.push(`Preferencia del cliente: ${pref.category} - ${pref.value}`);
        });

        // Based on time since last service
        if (profile.lastAnalysisDate) {
          const daysSince = Math.floor((Date.now() - new Date(profile.lastAnalysisDate).getTime()) / (1000 * 60 * 60 * 24));
          if (daysSince > 42) {
            recommendations.push(`Han pasado ${daysSince} días desde el último servicio. Considerar retoque de raíces.`);
          }
        }

        return recommendations;
      },

      getWarningsForClient: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        const warnings: string[] = [];

        // Check allergies
        warnings.push(...checkAllergies(profile.allergies));

        // Check patch tests
        warnings.push(...checkPatchTests(profile.patchTests));

        // Check formulas with bad results
        const badFormulas = profile.previousFormulas.filter(f => f.satisfaction <= 2);
        if (badFormulas.length > 0) {
          warnings.push(`⚠️ Evitar fórmulas similares a servicios con baja satisfacción`);
        }

        // Check avoided ingredients
        profile.avoidedIngredients.forEach(ingredient => {
          warnings.push(`⚠️ Evitar: ${ingredient}`);
        });

        // Check risk level
        if (profile.riskLevel === 'alto') {
          warnings.push(`⚠️ Cliente de alto riesgo - Extremar precauciones`);
        }

        // Check consent validity
        const lastConsent = get().getLastConsent(clientId);
        warnings.push(...checkConsent(lastConsent));

        return warnings;
      },

      calculateRiskLevel: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return 'bajo';

        let riskScore = 0;

        // Severe allergies
        const severeAllergies = profile.allergies.filter(a => a.severity === 'severa');
        riskScore += severeAllergies.length * 3;

        // Moderate allergies
        const moderateAllergies = profile.allergies.filter(a => a.severity === 'moderada');
        riskScore += moderateAllergies.length * 2;

        // Positive patch tests
        const positiveTests = profile.patchTests.filter(t => t.result === 'positivo');
        riskScore += positiveTests.length * 4;

        // Bad satisfaction formulas
        const badFormulas = profile.previousFormulas.filter(f => f.satisfaction <= 2);
        riskScore += badFormulas.length * 1;

        // Determine risk level
        if (riskScore >= 6) return 'alto';
        if (riskScore >= 3) return 'medio';
        return 'bajo';
      },

      getCompatibleFormulas: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile) return [];

        return profile.previousFormulas
          .filter(f => f.satisfaction >= 3)
          .sort((a, b) => b.satisfaction - a.satisfaction);
      },

      getLastPatchTest: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile || profile.patchTests.length === 0) return null;

        return profile.patchTests[0];
      },

      getLastConsent: (clientId: string) => {
        const profile = get().clientProfiles[clientId];
        if (!profile || profile.consentRecords.length === 0) return null;

        return profile.consentRecords[0];
      },

      initializeClientProfile: (clientId: string) => {
        if (get().clientProfiles[clientId]) return;

        const newProfile: ClientHistoryProfile = {
          clientId,
          allergies: [],
          preferences: [],
          hairEvolution: [],
          previousFormulas: [],
          patchTests: [],
          consentRecords: [],
          riskLevel: 'bajo',
          totalServices: 0,
          averageSatisfaction: 0,
          preferredBrands: [],
          avoidedIngredients: [],
          specialNotes: [],
        };

        set(state => ({
          clientProfiles: {
            ...state.clientProfiles,
            [clientId]: newProfile
          }
        }));
        
        historyLogger.info('Client profile initialized', { clientId });
      },

      syncWithSupabase: async () => {
        historyLogger.info('Sync with Supabase called');
        // Sync is handled on-demand per client
      },
    }),
    {
      name: 'client-history-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        clientProfiles: state.clientProfiles,
        isInitialized: state.isInitialized,
      }),
    }
  )
);