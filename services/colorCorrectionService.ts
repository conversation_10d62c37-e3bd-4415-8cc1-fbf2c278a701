import {
  ZoneColorAnalysis,
  HairState,
  Undertone,
  HairZone,
  UnwantedTone
} from '@/types/hair-diagnosis';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { logger } from '@/utils/logger';

export interface CorrectionNeed {
  type: 'neutralization' | 'pre-pigmentation' | 're-pigmentation' | 'pre-lightening';
  reason: string;
  zones: HairZone[];
  severity: 'light' | 'medium' | 'strong';
}

export interface CorrectionStep {
  stepNumber: number;
  title: string;
  products: string[];
  applicationTime: number;
  instructions: string[];
  rinse: boolean;
}

export interface ColorCorrectionAnalysis {
  needsCorrection: boolean;
  corrections: CorrectionNeed[];
  steps: CorrectionStep[];
  totalTime: number;
  warnings: string[];
}

// Tabla de neutralización basada en la rueda de color
const NEUTRALIZATION_MAP: Record<UnwantedTone, string> = {
  [UnwantedTone.ORANGE]: 'Azul/Cenizo',
  [UnwantedTone.YELLOW]: 'Violeta/Irisado',
  [UnwantedTone.GREEN]: 'Rojo/Cobrizo',
  [UnwantedTone.RED]: 'Verde/Mate',
  [UnwantedTone.ASHY_EXCESS]: 'Dorado/Cálido'
};

// Pigmentos necesarios por nivel para pre-pigmentación
const PRE_PIGMENTATION_MAP: Record<number, string> = {
  3: 'Rojo intenso',
  4: 'Rojo cobrizo',
  5: 'Cobrizo',
  6: 'Cobrizo dorado',
  7: 'Dorado',
  8: 'Dorado claro',
  9: 'Beige',
  10: 'Platino'
};

// Logger contextual para el servicio
const correctionLogger = logger.withContext('color-correction');

// Cache para resultados de análisis costosos
const analysisCache = new Map<string, ColorCorrectionAnalysis>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutos

interface CacheEntry {
  result: ColorCorrectionAnalysis;
  timestamp: number;
}

export class ColorCorrectionService {
  /**
   * Genera una clave de cache basada en los parámetros de entrada
   */
  private static generateCacheKey(
    currentAnalysis: Record<HairZone, ZoneColorAnalysis>,
    desiredAnalysis: DesiredColorAnalysisResult,
    unwantedTones?: Partial<Record<HairZone, UnwantedTone>>,
    brand?: string,
    line?: string
  ): string {
    const currentKey = JSON.stringify(currentAnalysis);
    const desiredKey = JSON.stringify(desiredAnalysis.general);
    const tonesKey = JSON.stringify(unwantedTones || {});
    return `${currentKey}-${desiredKey}-${tonesKey}-${brand || ''}-${line || ''}`;
  }

  /**
   * Verifica si un resultado está en cache y es válido
   */
  private static getCachedResult(cacheKey: string): ColorCorrectionAnalysis | null {
    const entry = analysisCache.get(cacheKey) as CacheEntry;
    if (entry && Date.now() - entry.timestamp < CACHE_TTL) {
      correctionLogger.info('Cache hit for color correction analysis');
      return entry.result;
    }

    if (entry) {
      // Limpiar entrada expirada
      analysisCache.delete(cacheKey);
    }

    return null;
  }

  /**
   * Guarda un resultado en cache
   */
  private static setCachedResult(cacheKey: string, result: ColorCorrectionAnalysis): void {
    analysisCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });

    // Limpiar cache si tiene demasiadas entradas
    if (analysisCache.size > 50) {
      const oldestKey = analysisCache.keys().next().value;
      analysisCache.delete(oldestKey);
    }
  }

  static detectCorrectionNeeds(
    currentAnalysis: Record<HairZone, ZoneColorAnalysis>,
    desiredAnalysis: DesiredColorAnalysisResult,
    unwantedTones?: Partial<Record<HairZone, UnwantedTone>>
  ): CorrectionNeed[] {
    correctionLogger.startTimer('detectCorrectionNeeds');
    const corrections: CorrectionNeed[] = [];

    // Detectar necesidad de neutralización
    if (unwantedTones) {
      Object.entries(unwantedTones).forEach(([zone, tone]) => {
        if (tone) {
          corrections.push({
            type: 'neutralization',
            reason: `Neutralizar tono ${tone} en ${zone}`,
            zones: [zone as HairZone],
            severity: this.calculateSeverity(currentAnalysis[zone as HairZone])
          });
        }
      });
    }

    // Optimizar cálculos de niveles
    const analysisValues = Object.values(currentAnalysis);
    const currentLevel = Math.min(...analysisValues.map(z => z.depthLevel));
    const desiredLevel = parseInt(desiredAnalysis.general.overallLevel);

    if (currentLevel > desiredLevel && currentLevel - desiredLevel >= 3) {
      const affectedZones = Object.entries(currentAnalysis)
        .filter(([_, analysis]) => analysis.depthLevel > desiredLevel + 2)
        .map(([zone, _]) => zone as HairZone);

      corrections.push({
        type: 'pre-pigmentation',
        reason: `Oscurecer de nivel ${currentLevel} a ${desiredLevel} requiere reponer pigmentos`,
        zones: affectedZones,
        severity: currentLevel - desiredLevel > 4 ? 'strong' : 'medium'
      });
    }
    
    // Detectar necesidad de re-pigmentación para cabellos decolorados - optimizado
    const analysisValues = Object.values(currentAnalysis);
    const hasDecoloredZones = analysisValues.some(z => z.state === HairState.BLEACHED);

    if (hasDecoloredZones && desiredLevel < 8) {
      // Optimizar para evitar múltiples iteraciones
      const decoloredZones = Object.entries(currentAnalysis)
        .filter(([_, analysis]) => analysis.state === HairState.BLEACHED)
        .map(([zone, _]) => zone as HairZone);

      corrections.push({
        type: 're-pigmentation',
        reason: 'Cabello decolorado necesita pigmentos cálidos para evitar resultado verdoso',
        zones: decoloredZones,
        severity: 'medium'
      });
    }

    // Detectar necesidad de pre-aclaración - optimizado
    if (desiredLevel > currentLevel && desiredLevel - currentLevel > 2) {
      // Usar zonas específicas en lugar de todas
      const affectedZones = Object.entries(currentAnalysis)
        .filter(([_, analysis]) => analysis.depthLevel < desiredLevel - 1)
        .map(([zone, _]) => zone as HairZone);

      corrections.push({
        type: 'pre-lightening',
        reason: `Aclarar de nivel ${currentLevel} a ${desiredLevel} requiere decoloración previa`,
        zones: affectedZones.length > 0 ? affectedZones : [HairZone.ROOTS, HairZone.MIDS, HairZone.ENDS],
        severity: desiredLevel - currentLevel > 4 ? 'strong' : 'medium'
      });
    }

    correctionLogger.endTimer('detectCorrectionNeeds');
    return corrections;
  }
  
  static generateCorrectionSteps(
    corrections: CorrectionNeed[],
    brand: string,
    line: string
  ): CorrectionStep[] {
    const steps: CorrectionStep[] = [];
    let stepNumber = 1;
    
    // Generar pasos de neutralización
    const neutralizations = corrections.filter(c => c.type === 'neutralization');
    if (neutralizations.length > 0) {
      const products: string[] = [];
      const zones = new Set<HairZone>();
      
      neutralizations.forEach(correction => {
        correction.zones.forEach(zone => zones.add(zone));
      });
      
      steps.push({
        stepNumber: stepNumber++,
        title: 'NEUTRALIZACIÓN DE MATICES NO DESEADOS',
        products: [
          `${brand} ${line} /2 Mate (10g)`,
          `${brand} ${line} /69 Violeta Rojizo (5g)`,
          'Oxidante 10 vol (30g)'
        ],
        applicationTime: 20,
        instructions: [
          `Aplicar en las zonas con matices no deseados: ${Array.from(zones).join(', ')}`,
          'Peinar para distribuir uniformemente',
          'NO aplicar calor',
          'Vigilar el proceso cada 5 minutos'
        ],
        rinse: true
      });
    }
    
    // Generar pasos de pre-pigmentación
    const prePigmentations = corrections.filter(c => c.type === 'pre-pigmentation' || c.type === 're-pigmentation');
    if (prePigmentations.length > 0) {
      const targetLevel = 6; // Nivel medio para pre-pigmentación
      
      steps.push({
        stepNumber: stepNumber++,
        title: 'PRE-PIGMENTACIÓN',
        products: [
          `${brand} ${line} ${targetLevel}/4 Cobrizo (20g)`,
          'Agua tibia (10ml)',
          'NO usar oxidante'
        ],
        applicationTime: 15,
        instructions: [
          'Mezclar el tinte con agua hasta obtener consistencia cremosa',
          'Aplicar en medios y puntas (evitar raíces si están naturales)',
          'Peinar para distribuir',
          'NO enjuagar antes del siguiente paso'
        ],
        rinse: false
      });
    }
    
    // Generar pasos de pre-aclaración
    const preLightenings = corrections.filter(c => c.type === 'pre-lightening');
    if (preLightenings.length > 0) {
      steps.push({
        stepNumber: stepNumber++,
        title: 'DECOLORACIÓN CONTROLADA',
        products: [
          'Polvo decolorante (30g)',
          'Oxidante 20-30 vol según necesidad (60g)',
          'Plex/Protector capilar (según instrucciones)'
        ],
        applicationTime: 35,
        instructions: [
          'Aplicar primero en zonas más oscuras',
          'Evitar el cuero cabelludo (dejar 1cm)',
          'Controlar cada 10 minutos',
          'Detener cuando alcance el fondo de aclaración deseado'
        ],
        rinse: true
      });
    }
    
    return steps;
  }
  
  static analyzeColorCorrection(
    currentAnalysis: Record<HairZone, ZoneColorAnalysis>,
    desiredAnalysis: DesiredColorAnalysisResult,
    unwantedTones?: Partial<Record<HairZone, UnwantedTone>>,
    brand: string = 'L\'Oréal',
    line: string = 'Majirel'
  ): ColorCorrectionAnalysis {
    correctionLogger.startTimer('analyzeColorCorrection');

    // Verificar cache primero
    const cacheKey = this.generateCacheKey(currentAnalysis, desiredAnalysis, unwantedTones, brand, line);
    const cachedResult = this.getCachedResult(cacheKey);

    if (cachedResult) {
      correctionLogger.endTimer('analyzeColorCorrection');
      return cachedResult;
    }

    // Realizar análisis si no está en cache
    const corrections = this.detectCorrectionNeeds(currentAnalysis, desiredAnalysis, unwantedTones);
    const steps = this.generateCorrectionSteps(corrections, brand, line);

    // Optimizar cálculo de tiempo total
    const totalTime = steps.reduce((sum, step) => sum + step.applicationTime, 0);

    // Generar advertencias de forma más eficiente
    const warnings: string[] = [];
    const hasStrongCorrections = corrections.some(c => c.severity === 'strong');
    const hasPreLightening = corrections.some(c => c.type === 'pre-lightening');

    if (hasStrongCorrections) {
      warnings.push('Proceso complejo: Se recomienda realizarlo en varias sesiones');
    }

    if (hasPreLightening) {
      warnings.push('La decoloración puede causar daño: Usar protector capilar obligatorio');
    }

    if (totalTime > 60) {
      warnings.push(`Tiempo total elevado (${totalTime} min): Considerar dividir el proceso`);
    }

    const result: ColorCorrectionAnalysis = {
      needsCorrection: corrections.length > 0,
      corrections,
      steps,
      totalTime,
      warnings
    };

    // Guardar en cache
    this.setCachedResult(cacheKey, result);

    correctionLogger.endTimer('analyzeColorCorrection');
    return result;
  }
  
  private static calculateSeverity(analysis: ZoneColorAnalysis): 'light' | 'medium' | 'strong' {
    // Lógica simplificada - en producción sería más compleja
    if (analysis.damage === 'Alto') return 'strong';
    if (analysis.damage === 'Medio') return 'medium';
    return 'light';
  }
  
  static formatCorrectionForFormula(correctionAnalysis: ColorCorrectionAnalysis): string {
    correctionLogger.startTimer('formatCorrectionForFormula');

    if (!correctionAnalysis.needsCorrection) {
      correctionLogger.endTimer('formatCorrectionForFormula');
      return '';
    }

    // Usar array para mejor rendimiento que concatenación de strings
    const parts: string[] = [
      '\n⚠️ CORRECCIÓN DE COLOR NECESARIA:',
      '━━━━━━━━━━━━━━━━━━━━━━━━━━━\n'
    ];

    // Añadir razones de forma más eficiente
    correctionAnalysis.corrections.forEach(correction => {
      parts.push(`• ${correction.reason}`);
    });

    parts.push(''); // Línea vacía

    // Añadir pasos de forma optimizada
    correctionAnalysis.steps.forEach(step => {
      parts.push(`PASO ${step.stepNumber} - ${step.title} (${step.applicationTime} min):`);

      // Productos
      step.products.forEach(product => {
        parts.push(`• ${product}`);
      });

      // Instrucciones
      parts.push('\nInstrucciones:');
      step.instructions.forEach((instruction, index) => {
        parts.push(`${index + 1}. ${instruction}`);
      });

      // Estado de enjuague
      if (step.rinse) {
        parts.push('\n✓ Enjuagar completamente antes del siguiente paso');
      } else {
        parts.push('\n✗ NO enjuagar - Proceder directamente al siguiente paso');
      }

      parts.push(''); // Línea vacía
    });

    // Añadir advertencias si existen
    if (correctionAnalysis.warnings.length > 0) {
      parts.push('ADVERTENCIAS:');
      correctionAnalysis.warnings.forEach(warning => {
        parts.push(`⚠️ ${warning}`);
      });
    }

    correctionLogger.endTimer('formatCorrectionForFormula');
    return parts.join('\n');
  }

  /**
   * Limpia el cache de análisis (útil para testing o liberación de memoria)
   */
  static clearCache(): void {
    analysisCache.clear();
    correctionLogger.info('Color correction cache cleared');
  }

  /**
   * Obtiene estadísticas del cache
   */
  static getCacheStats(): { size: number; hitRate: number } {
    return {
      size: analysisCache.size,
      hitRate: 0 // Se podría implementar un contador de hits/misses
    };
  }
}