import { useInventoryStore } from '@/stores/inventory-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { FormulationConsumption, Product } from '@/types/inventory';
import { ColorFormula } from '@/types/formulation';

interface ProductMatch {
  product: Product;
  matchScore: number;
  matchType: 'exact' | 'partial' | 'fuzzy';
}

export class InventoryConsumptionService {
  /**
   * Encuentra productos en el inventario que coincidan con el nombre dado
   */
  static findMatchingProducts(productName: string, brand?: string): ProductMatch[] {
    const inventoryStore = useInventoryStore.getState();
    const products = inventoryStore.products;
    
    const normalizedName = this.normalizeProductName(productName);
    const normalizedBrand = brand ? brand.toLowerCase().trim() : '';
    
    const matches: ProductMatch[] = [];
    
    products.forEach(product => {
      const productNormalized = this.normalizeProductName(product.name);
      const brandNormalized = product.brand.toLowerCase().trim();
      
      let matchScore = 0;
      let matchType: ProductMatch['matchType'] = 'fuzzy';
      
      // Exact match
      if (productNormalized === normalizedName) {
        matchScore = 100;
        matchType = 'exact';
      }
      // Contains match
      else if (productNormalized.includes(normalizedName) || normalizedName.includes(productNormalized)) {
        matchScore = 80;
        matchType = 'partial';
      }
      // Common variations
      else if (this.areProductsSimilar(productNormalized, normalizedName)) {
        matchScore = 60;
        matchType = 'fuzzy';
      }
      
      // Brand matching bonus
      if (normalizedBrand && brandNormalized === normalizedBrand) {
        matchScore += 20;
      }
      
      if (matchScore > 50) {
        matches.push({ product, matchScore, matchType });
      }
    });
    
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }
  
  /**
   * Normaliza nombres de productos para mejor matching
   */
  private static normalizeProductName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/oxidante|developer|peróxido|peroxide/gi, 'oxidante')
      .replace(/decolorante|bleach|polvo/gi, 'decolorante')
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '');
  }
  
  /**
   * Verifica si dos productos son similares basándose en variaciones comunes
   */
  private static areProductsSimilar(name1: string, name2: string): boolean {
    const variations = [
      ['oxidante', 'developer', 'peroxide', 'peróxido', 'activador'],
      ['decolorante', 'bleach', 'polvo decolorante', 'lightener'],
      ['olaplex', 'plex', 'bond'],
      ['10 vol', '3%', '10v'],
      ['20 vol', '6%', '20v'],
      ['30 vol', '9%', '30v'],
      ['40 vol', '12%', '40v'],
    ];
    
    for (const group of variations) {
      const hasName1 = group.some(v => name1.includes(v));
      const hasName2 = group.some(v => name2.includes(v));
      if (hasName1 && hasName2) return true;
    }
    
    return false;
  }
  
  /**
   * Calcula el costo de una formulación usando precios del inventario
   */
  static async calculateFormulationCost(formula: ColorFormula): Promise<FormulationConsumption> {
    const inventoryStore = useInventoryStore.getState();
    const configStore = useSalonConfigStore.getState();
    
    const items: FormulationConsumption['items'] = [];
    let totalCost = 0;
    let hasInsufficientStock = false;
    const insufficientProducts: string[] = [];
    
    // Procesar cada producto en la fórmula
    const formulaItems = this.parseFormula(formula);
    
    for (const item of formulaItems) {
      const matches = this.findMatchingProducts(item.name, item.brand);
      const bestMatch = matches[0];
      
      if (bestMatch && bestMatch.matchScore > 60) {
        const product = bestMatch.product;
        const amount = item.amount;
        const unitCost = product.costPerUnit;
        const itemCost = amount * unitCost;
        
        const isStockSufficient = product.currentStock >= amount;
        if (!isStockSufficient) {
          hasInsufficientStock = true;
          insufficientProducts.push(product.name);
        }
        
        items.push({
          productId: product.id,
          productName: product.name,
          amount,
          unit: product.unitType,
          unitCost,
          totalCost: itemCost,
          availableStock: product.currentStock,
          isStockSufficient,
        });
        
        totalCost += itemCost;
      } else {
        // Si no se encuentra el producto, usar precio estimado
        const estimatedCost = this.getEstimatedCost(item.name, item.amount);
        
        items.push({
          productId: '',
          productName: `${item.name} (Estimado)`,
          amount: item.amount,
          unit: item.unit || 'g',
          unitCost: estimatedCost / item.amount,
          totalCost: estimatedCost,
          availableStock: 0,
          isStockSufficient: false,
        });
        
        totalCost += estimatedCost;
      }
    }
    
    return {
      formulationId: formula.id || '',
      items,
      totalCost,
      hasInsufficientStock,
      insufficientProducts,
    };
  }
  
  /**
   * Parsea una fórmula para extraer productos y cantidades
   */
  private static parseFormula(formula: ColorFormula): Array<{
    name: string;
    brand?: string;
    amount: number;
    unit?: string;
  }> {
    const items: Array<{ name: string; brand?: string; amount: number; unit?: string }> = [];
    
    // Parsear tintes
    if (formula.colors && formula.colors.length > 0) {
      formula.colors.forEach(color => {
        const amount = typeof color.amount === 'number' ? color.amount : 0;
        items.push({
          name: `${formula.brand} ${color.tone}`,
          brand: formula.brand,
          amount,
          unit: 'g',
        });
      });
    }
    
    // Parsear oxidante
    if (formula.developerVolume) {
      // Calcular la cantidad de oxidante basándose en el ratio
      let developerAmount = 0;
      const totalColorAmount = formula.colors.reduce((sum, color) => sum + (color.amount || 0), 0);
      
      if (formula.developerRatio) {
        // Si el ratio es "1:1.5", "1:2", etc.
        const ratioMatch = formula.developerRatio.match(/1:(\d+\.?\d*)/);
        if (ratioMatch) {
          const ratioMultiplier = parseFloat(ratioMatch[1]);
          developerAmount = totalColorAmount * ratioMultiplier;
        } else {
          // Default 1:1
          developerAmount = totalColorAmount;
        }
      } else {
        // Default 1:1
        developerAmount = totalColorAmount;
      }
      
      items.push({
        name: `Oxidante ${formula.developerVolume} Vol`,
        amount: developerAmount,
        unit: 'ml',
      });
    }
    
    // Parsear aditivos
    if (formula.additives && formula.additives.length > 0) {
      formula.additives.forEach(additive => {
        const amountMatch = additive.match(/(\d+)\s*(ml|g|gr)/i);
        const amount = amountMatch ? parseFloat(amountMatch[1]) : 10;
        const unit = amountMatch && amountMatch[2] ? amountMatch[2].toLowerCase() : 'ml';
        
        items.push({
          name: additive.replace(/\d+\s*(ml|g|gr)/gi, '').trim(),
          amount,
          unit: unit === 'gr' ? 'g' : unit,
        });
      });
    }
    
    return items;
  }
  
  /**
   * Obtiene un costo estimado para productos no encontrados
   */
  private static getEstimatedCost(productName: string, amount: number): number {
    const normalized = productName.toLowerCase();
    
    if (normalized.includes('oxidante') || normalized.includes('developer')) {
      return amount * 0.005; // €0.005/ml
    }
    if (normalized.includes('decolorante') || normalized.includes('bleach')) {
      return amount * 0.03; // €0.03/g
    }
    if (normalized.includes('olaplex') || normalized.includes('tratamiento')) {
      return amount * 0.5; // €0.50/ml
    }
    
    // Default para tintes
    return amount * 0.15; // €0.15/g
  }
  
  /**
   * Consume productos del inventario para una formulación
   */
  static async consumeFormulation(
    formulationId: string,
    formula: ColorFormula,
    clientName: string
  ): Promise<{ success: boolean; errors: string[] }> {
    const inventoryStore = useInventoryStore.getState();
    const configStore = useSalonConfigStore.getState();
    
    // Verificar si el consumo automático está habilitado
    if (!configStore.configuration.autoConsumption) {
      return { success: true, errors: [] };
    }
    
    // Verificar nivel de control
    if (configStore.configuration.inventoryControlLevel === 'solo-formulas') {
      return { success: true, errors: [] };
    }
    
    const errors: string[] = [];
    const consumptions: Array<{ productId: string; quantity: number }> = [];
    
    // Obtener el análisis de consumo
    const analysis = await this.calculateFormulationCost(formula);
    
    // Verificar stock si está configurado
    if (configStore.configuration.requireStockValidation && analysis.hasInsufficientStock) {
      errors.push(`Stock insuficiente para: ${analysis.insufficientProducts.join(', ')}`);
      return { success: false, errors };
    }
    
    // Preparar consumos
    for (const item of analysis.items) {
      if (item.productId) {
        consumptions.push({
          productId: item.productId,
          quantity: item.amount,
        });
      }
    }
    
    // Ejecutar consumos
    try {
      await inventoryStore.consumeProducts(consumptions, formulationId, clientName);
      return { success: true, errors: [] };
    } catch (error) {
      errors.push(`Error al consumir productos: ${error}`);
      return { success: false, errors };
    }
  }
  
  /**
   * Verifica si hay stock suficiente para una formulación
   */
  static async checkStock(formula: ColorFormula): Promise<{
    hasStock: boolean;
    missingProducts: string[];
  }> {
    const analysis = await this.calculateFormulationCost(formula);
    
    return {
      hasStock: !analysis.hasInsufficientStock,
      missingProducts: analysis.insufficientProducts,
    };
  }
}