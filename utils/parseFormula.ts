import { ColorFormula } from '@/types/formulation';

/**
 * Parse formula text to extract structured data
 * Handles various formula formats from different brands
 */
export function parseFormulaText(formulaText: string): ColorFormula {
  // Extract brand and line from first line
  const lines = formulaText.split('\n');
  const firstLine = lines[0] || '';
  
  // Try to extract brand and line
  const brandLineMatch = firstLine.match(/^([^:]+):\s*$/);
  const [brand, line] = brandLineMatch 
    ? brandLineMatch[1].split(' ').filter(Boolean)
    : ['Unknown', 'Unknown'];

  // Extract colors/tones
  const colors: ColorFormula['colors'] = [];
  const colorRegex = /[-–]\s*([\w\s]+)\s+(\d+[/.,-]?\d*)\s*\((\d+)g\)/g;
  let colorMatch;
  
  while ((colorMatch = colorRegex.exec(formulaText)) !== null) {
    colors.push({
      tone: colorMatch[2],
      amount: parseInt(colorMatch[3])
    });
  }

  // Extract developer/oxidant info
  const developerMatch = formulaText.match(/[Oo]xidante\s+(\d+)\s*vol/);
  const developerVolume = developerMatch ? parseInt(developerMatch[1]) : 20;
  
  const ratioMatch = formulaText.match(/\(?(1:\d+(?:\.\d+)?)\)?/);
  const developerRatio = ratioMatch ? ratioMatch[1] : "1:1.5";

  // Extract processing time
  const timeMatch = formulaText.match(/(\d+)\s*min/);
  const processingTime = timeMatch ? parseInt(timeMatch[1]) : 35;

  // Extract additives
  const additives: string[] = [];
  if (formulaText.includes('Olaplex')) {
    additives.push('Olaplex');
  }
  if (formulaText.includes('protector') || formulaText.includes('Protector')) {
    additives.push('Protector');
  }

  // Default values if nothing found
  if (colors.length === 0) {
    colors.push({
      tone: '7/1',
      amount: 60
    });
  }

  return {
    brand: brand || 'Unknown',
    line: line || 'Unknown',
    colors,
    developerVolume,
    developerRatio,
    additives,
    processingTime,
    formulaText
  };
}

/**
 * Calculate simple formula cost without inventory
 * Used as fallback when inventory is not available
 */
export function calculateSimpleFormulaCost(formulaText: string) {
  const formula = parseFormulaText(formulaText);
  
  // Simple cost estimates per gram
  const costPerGramTint = 0.15; // €0.15 per gram
  const costPerGramDeveloper = 0.05; // €0.05 per gram
  const additiveCost = 2.5; // €2.50 per additive
  
  let totalCost = 0;
  const items = [];
  
  // Calculate tint costs
  formula.colors.forEach(color => {
    const cost = color.amount * costPerGramTint;
    totalCost += cost;
    items.push({
      product: `Tinte ${color.tone}`,
      amount: `${color.amount}g`,
      unitCost: costPerGramTint,
      totalCost: cost
    });
  });
  
  // Calculate developer cost
  const totalTintAmount = formula.colors.reduce((sum, c) => sum + c.amount, 0);
  const developerAmount = formula.developerRatio === "1:2" 
    ? totalTintAmount * 2 
    : totalTintAmount * 1.5;
  
  const developerCost = developerAmount * costPerGramDeveloper;
  totalCost += developerCost;
  
  items.push({
    product: `Oxidante ${formula.developerVolume} vol`,
    amount: `${Math.round(developerAmount)}g`,
    unitCost: costPerGramDeveloper,
    totalCost: developerCost
  });
  
  // Add additives
  formula.additives.forEach(additive => {
    totalCost += additiveCost;
    items.push({
      product: additive,
      amount: '1 dosis',
      unitCost: additiveCost,
      totalCost: additiveCost
    });
  });
  
  // Apply standard markup (3x material cost)
  const suggestedServicePrice = totalCost * 3;
  const profitMargin = suggestedServicePrice - totalCost;
  
  return {
    items,
    totalMaterialCost: Math.round(totalCost * 100) / 100,
    suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
    profitMargin: Math.round(profitMargin * 100) / 100
  };
}