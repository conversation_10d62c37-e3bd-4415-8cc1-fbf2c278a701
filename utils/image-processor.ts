/**
 * Image Processor Service
 * 
 * Servicio centralizado para el procesamiento de imágenes
 * Maneja compresión, validación de calidad y generación de hashes
 */

import * as ImageManipulator from 'expo-image-manipulator';
import * as Crypto from 'expo-crypto';
import { logger } from './logger';

// Configuración de compresión por propósito
const COMPRESSION_PROFILES = {
  diagnosis: {
    maxWidth: 400,
    quality: 0.5,
    maxSizeKB: 500,
    fallbackWidth: 300,
    fallbackQuality: 0.3
  },
  desired: {
    maxWidth: 350,
    quality: 0.4,
    maxSizeKB: 400,
    fallbackWidth: 250,
    fallbackQuality: 0.3
  },
  thumbnail: {
    maxWidth: 150,
    quality: 0.6,
    maxSizeKB: 50,
    fallbackWidth: 100,
    fallbackQuality: 0.4
  },
  storage: {
    maxWidth: 800,
    quality: 0.7,
    maxSizeKB: 1000,
    fallbackWidth: 600,
    fallbackQuality: 0.5
  }
} as const;

type CompressionPurpose = keyof typeof COMPRESSION_PROFILES;

interface ValidationIssue {
  type: 'size' | 'dimension' | 'format' | 'quality';
  message: string;
  severity: 'warning' | 'error';
}

interface CompressionResult {
  base64: string;
  sizeKB: number;
  width: number;
  height: number;
  compressionRatio: number;
}

export class ImageProcessor {
  private static compressionCache = new Map<string, CompressionResult>();
  private static cacheTimestamps = new Map<string, number>();
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutos
  private static readonly MAX_CACHE_SIZE = 20; // Máximo 20 entradas en cache

  /**
   * Comprime una imagen según el propósito especificado
   * Utiliza cache para evitar recompresiones innecesarias
   */
  static async compressForAI(
    uri: string, 
    purpose: 'diagnosis' | 'desired'
  ): Promise<string> {
    const cacheKey = `${uri}-${purpose}`;
    const cached = this.compressionCache.get(cacheKey);
    
    if (cached && this.isCacheValid(cacheKey)) {
      logger.debug('Using cached compression for:', purpose);
      return cached.base64;
    }

    const profile = COMPRESSION_PROFILES[purpose];
    
    try {
      logger.debug(`Compressing image for ${purpose}...`);
      
      // Primera compresión
      let result = await this.compress(uri, profile.maxWidth, profile.quality);
      
      // Si excede el tamaño máximo, recomprimir
      if (result.sizeKB > profile.maxSizeKB) {
        logger.warn(`Image too large (${result.sizeKB}KB), recompressing...`);
        result = await this.compress(
          uri, 
          profile.fallbackWidth, 
          profile.fallbackQuality
        );
        
        // Si aún es muy grande, compresión agresiva
        if (result.sizeKB > profile.maxSizeKB) {
          logger.warn('Applying aggressive compression...');
          result = await this.compress(
            uri,
            profile.fallbackWidth * 0.8,
            0.25
          );
        }
      }

      logger.info(`Final compression: ${result.sizeKB}KB (${result.compressionRatio.toFixed(1)}x reduction)`);
      
      // Guardar en cache con mejor gestión de memoria
      this.compressionCache.set(cacheKey, result);
      this.cacheTimestamps.set(cacheKey, Date.now());

      // Limpiar cache si excede el tamaño máximo
      if (this.compressionCache.size > this.MAX_CACHE_SIZE) {
        this.pruneCache();
      }

      return result.base64;
      
    } catch (error) {
      logger.error('Compression failed:', error);
      throw new Error('Error al comprimir la imagen');
    }
  }

  /**
   * Valida la calidad y características de una imagen
   */
  static async validateQuality(uri: string): Promise<{
    valid: boolean;
    issues: ValidationIssue[];
  }> {
    const issues: ValidationIssue[] = [];
    
    try {
      // Obtener información de la imagen
      const info = await ImageManipulator.manipulateAsync(
        uri,
        [],
        { base64: false }
      );

      // Validar dimensiones
      if (info.width < 200 || info.height < 200) {
        issues.push({
          type: 'dimension',
          message: 'La imagen es demasiado pequeña (mínimo 200x200)',
          severity: 'error'
        });
      } else if (info.width < 400 || info.height < 400) {
        issues.push({
          type: 'dimension',
          message: 'Se recomienda una imagen de al menos 400x400 para mejor análisis',
          severity: 'warning'
        });
      }

      // Validar proporción
      const aspectRatio = info.width / info.height;
      if (aspectRatio < 0.5 || aspectRatio > 2) {
        issues.push({
          type: 'dimension',
          message: 'La proporción de la imagen no es óptima',
          severity: 'warning'
        });
      }

      // Estimar tamaño del archivo (aproximado)
      const tempResult = await ImageManipulator.manipulateAsync(
        uri,
        [],
        { base64: true, compress: 1 }
      );
      const sizeKB = (tempResult.base64.length * 3) / 4 / 1024;
      
      if (sizeKB > 5000) {
        issues.push({
          type: 'size',
          message: 'La imagen es demasiado grande (máximo 5MB)',
          severity: 'error'
        });
      }

      // Validación básica de calidad por tamaño
      // (imágenes muy pequeñas en KB suelen ser de baja calidad)
      if (sizeKB < 50 && info.width > 500) {
        issues.push({
          type: 'quality',
          message: 'La imagen parece tener muy baja calidad',
          severity: 'warning'
        });
      }

      const hasErrors = issues.some(i => i.severity === 'error');
      
      return {
        valid: !hasErrors,
        issues
      };
      
    } catch (error) {
      logger.error('Quality validation failed:', error);
      return {
        valid: false,
        issues: [{
          type: 'format',
          message: 'No se pudo validar la imagen',
          severity: 'error'
        }]
      };
    }
  }

  /**
   * Genera un hash único para una imagen base64
   * Útil para cache y deduplicación
   */
  static async generateHash(base64: string): Promise<string> {
    try {
      // Tomar solo una porción del base64 para el hash (más eficiente)
      const sample = base64.substring(0, 1000) + base64.length;
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        sample
      );
      return hash.substring(0, 16); // Primeros 16 caracteres del hash
    } catch (error) {
      logger.error('Hash generation failed:', error);
      // Fallback a un hash simple
      return `${base64.length}-${Date.now()}`;
    }
  }

  /**
   * Comprime una imagen con los parámetros especificados
   */
  private static async compress(
    uri: string,
    width: number,
    quality: number
  ): Promise<CompressionResult> {
    const startTime = Date.now();
    
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width } }],
      { 
        compress: quality,
        format: ImageManipulator.SaveFormat.JPEG,
        base64: true
      }
    );

    const sizeKB = (result.base64.length * 3) / 4 / 1024;
    
    // Estimar tamaño original (aproximado)
    const originalResult = await ImageManipulator.manipulateAsync(
      uri,
      [],
      { base64: true, compress: 1 }
    );
    const originalSizeKB = (originalResult.base64.length * 3) / 4 / 1024;
    
    logger.debug(`Compression took ${Date.now() - startTime}ms`);
    
    return {
      base64: result.base64,
      sizeKB: Math.round(sizeKB * 10) / 10,
      width: result.width,
      height: result.height,
      compressionRatio: originalSizeKB / sizeKB
    };
  }

  /**
   * Verifica si una entrada de cache sigue siendo válida
   */
  private static isCacheValid(key: string): boolean {
    const timestamp = this.cacheTimestamps.get(key);
    if (!timestamp) return false;

    const isValid = Date.now() - timestamp < this.CACHE_TTL;
    if (!isValid) {
      this.compressionCache.delete(key);
      this.cacheTimestamps.delete(key);
    }

    return isValid;
  }

  /**
   * Elimina las entradas más antiguas del cache
   */
  private static pruneCache(): void {
    const entries = Array.from(this.cacheTimestamps.entries())
      .sort(([, a], [, b]) => a - b); // Ordenar por timestamp

    const toRemove = entries.slice(0, Math.floor(this.MAX_CACHE_SIZE / 2));

    toRemove.forEach(([key]) => {
      this.compressionCache.delete(key);
      this.cacheTimestamps.delete(key);
    });

    logger.debug(`Pruned ${toRemove.length} entries from image cache`);
  }

  /**
   * Limpia el cache de compresión
   */
  static clearCache(): void {
    this.compressionCache.clear();
    this.cacheTimestamps.clear();
    logger.info('Image compression cache cleared');
  }

  /**
   * Obtiene estadísticas del cache
   */
  static getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.compressionCache.size,
      entries: Array.from(this.compressionCache.keys())
    };
  }
}