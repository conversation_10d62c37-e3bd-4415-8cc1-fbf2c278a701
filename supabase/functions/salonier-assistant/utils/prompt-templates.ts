/**
 * Prompt Templates System for Salonier Assistant
 * Manages all AI prompts with optimization levels
 */

import { TemplateType, TemplateContext, RegionalConfig, FormulaConfig } from '../types.ts'
import { OPTIMIZATION_TARGETS } from '../constants.ts'

export class PromptTemplates {
  /**
   * Selects optimal template based on context
   */
  static selectOptimalTemplate(context: TemplateContext): TemplateType {
    // Enterprise always gets full analysis
    if (context.userTier === 'enterprise') return 'full'
    
    // High quality images can use optimized templates
    if (context.imageQuality === 'high') {
      return context.userTier === 'pro' ? 'optimized' : 'minimal'
    }
    
    // Low quality images need more detailed prompts
    if (context.imageQuality === 'low') {
      return context.userTier === 'pro' ? 'full' : 'optimized'
    }
    
    // Default based on tier
    return OPTIMIZATION_TARGETS.defaultTemplate[context.userTier]
  }

  /**
   * Get diagnosis prompt with optimization level
   */
  static getDiagnosisPrompt(template: TemplateType = 'full', lang: 'es' | 'en' = 'es'): string {
    const prompts = {
      es: {
        full: `Eres un experto colorista profesional analizando el cabello de un cliente.
  
Analiza la imagen proporcionada y devuelve un análisis COMPLETO en formato JSON con EXACTAMENTE esta estructura:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta",
  "overallTone": "nombre del tono general",
  "overallUndertone": "Frío|Cálido|Neutro",
  "averageDepthLevel": número decimal (1-10),
  "zoneAnalysis": {
    "roots": {
      "depth": número decimal (1-10),
      "tone": "tono específico",
      "undertone": "Frío|Cálido|Neutro",
      "percentage": número (0-100),
      "state": "Natural|Procesado|Decolorado|Teñido",
      "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
      "grayPercentage": número (0-100),
      "grayType": "Blanco|Gris|Mixto|null",
      "grayPattern": "Uniforme|Localizado|Disperso|null",
      "cuticleState": "Cerrada|Abierta|Dañada",
      "damage": "Ninguno|Leve|Moderado|Severo",
      "elasticity": "Buena|Regular|Mala",
      "porosity": "Baja|Media|Alta",
      "resistance": "Fuerte|Media|Débil"
    },
    "mids": { /* misma estructura */ },
    "ends": { /* misma estructura */ }
  },
  "detectedChemicalProcess": "Coloración|Decoloración|Permanente|Alisado|Ninguno|null",
  "estimatedLastProcessDate": "texto descriptivo",
  "detectedRisks": { 
    "metallic": boolean, 
    "henna": boolean, 
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": minutos estimados,
  "overallCondition": "descripción detallada",
  "recommendations": ["lista de al menos 3 recomendaciones específicas"],
  "overallConfidence": porcentaje (0-100)
}

IMPORTANTE: Analiza CADA zona por separado con TODOS los campos.`,

        optimized: `Experto colorista analizando cabello. Devuelve JSON:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta", 
  "overallTone": "tono",
  "overallUndertone": "Frío|Cálido|Neutro",
  "averageDepthLevel": 1-10,
  "zoneAnalysis": {
    "roots": {
      "depth": 1-10,
      "tone": "tono",
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": 0-100,
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta"
    },
    "mids": { /* igual */ },
    "ends": { /* igual */ }
  },
  "detectedChemicalProcess": "tipo o null",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "overallCondition": "descripción",
  "recommendations": ["3 recomendaciones"],
  "overallConfidence": 0-100
}`,

        minimal: `Analiza cabello, JSON exacto:
{"hairThickness":"Fino|Medio|Grueso","hairDensity":"Baja|Media|Alta","overallTone":"tono","averageDepthLevel":1-10,"zoneAnalysis":{"roots":{"depth":1-10,"tone":"tono","state":"Natural|Procesado|Decolorado|Teñido","grayPercentage":0-100},"mids":{},"ends":{}},"serviceComplexity":"simple|medium|complex","overallCondition":"desc","recommendations":["3 items"]}`
      },
      
      en: {
        full: `You are an expert colorist analyzing a client's hair.

Analyze the image and return a COMPLETE JSON analysis with EXACTLY this structure:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "overall tone name",
  "overallUndertone": "Cool|Warm|Neutral",
  "averageDepthLevel": decimal number (1-10),
  "zoneAnalysis": {
    "roots": {
      "depth": decimal (1-10),
      "tone": "specific tone",
      "undertone": "Cool|Warm|Neutral",
      "percentage": number (0-100),
      "state": "Natural|Processed|Bleached|Colored",
      "unwantedTone": "Green|Orange|Yellow|Red|Violet|null",
      "grayPercentage": number (0-100),
      "grayType": "White|Gray|Mixed|null",
      "grayPattern": "Uniform|Localized|Scattered|null",
      "cuticleState": "Closed|Open|Damaged",
      "damage": "None|Light|Moderate|Severe",
      "elasticity": "Good|Fair|Poor",
      "porosity": "Low|Medium|High",
      "resistance": "Strong|Medium|Weak"
    },
    "mids": { /* same structure */ },
    "ends": { /* same structure */ }
  },
  "detectedChemicalProcess": "Coloring|Bleaching|Perm|Straightening|None|null",
  "estimatedLastProcessDate": "descriptive text",
  "detectedRisks": {
    "metallic": boolean,
    "henna": boolean,
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": estimated minutes,
  "overallCondition": "detailed description",
  "recommendations": ["list of at least 3 specific recommendations"],
  "overallConfidence": percentage (0-100)
}`,

        optimized: `Hair expert analysis. Return JSON:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "tone",
  "overallUndertone": "Cool|Warm|Neutral",
  "averageDepthLevel": 1-10,
  "zoneAnalysis": {
    "roots": {
      "depth": 1-10,
      "tone": "tone",
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": 0-100,
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High"
    },
    "mids": { /* same */ },
    "ends": { /* same */ }
  },
  "detectedChemicalProcess": "type or null",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "overallCondition": "description",
  "recommendations": ["3 recommendations"],
  "overallConfidence": 0-100
}`,

        minimal: `Analyze hair, exact JSON:
{"hairThickness":"Fine|Medium|Thick","hairDensity":"Low|Medium|High","overallTone":"tone","averageDepthLevel":1-10,"zoneAnalysis":{"roots":{"depth":1-10,"tone":"tone","state":"Natural|Processed|Bleached|Colored","grayPercentage":0-100},"mids":{},"ends":{}},"serviceComplexity":"simple|medium|complex","overallCondition":"desc","recommendations":["3 items"]}`
      }
    }

    return prompts[lang][template]
  }

  /**
   * Get desired look analysis prompt
   */
  static getDesiredLookPrompt(
    currentLevel: number, 
    template: TemplateType = 'full', 
    lang: 'es' | 'en' = 'es'
  ): string {
    const prompts = {
      es: {
        full: `Analiza esta imagen de referencia de color de cabello deseado.
  
Considerando que el nivel actual del cliente es ${currentLevel}, proporciona un análisis en formato JSON:
{
  "detectedLevel": número decimal del nivel objetivo,
  "detectedTone": "tono principal detectado",
  "detectedTechnique": "técnica de aplicación detectada",
  "detectedTones": ["lista de tonos presentes"],
  "viabilityScore": 0-100,
  "estimatedSessions": número de sesiones necesarias,
  "requiredProcesses": ["procesos necesarios"],
  "confidence": porcentaje de confianza,
  "warnings": ["advertencias si las hay"]
}`,

        optimized: `Analiza color deseado. Nivel actual: ${currentLevel}. JSON:
{
  "detectedLevel": nivel objetivo,
  "detectedTone": "tono principal",
  "detectedTechnique": "técnica",
  "detectedTones": ["tonos"],
  "viabilityScore": 0-100,
  "estimatedSessions": número,
  "requiredProcesses": ["procesos"],
  "confidence": 0-100
}`,

        minimal: `Color deseado, nivel actual:${currentLevel}. JSON:
{"detectedLevel":num,"detectedTone":"tono","detectedTechnique":"técnica","detectedTones":["array"],"viabilityScore":0-100,"estimatedSessions":num,"requiredProcesses":["array"],"confidence":0-100}`
      },
      
      en: {
        full: `Analyze this desired hair color reference image.

Considering the client's current level is ${currentLevel}, provide a JSON analysis:
{
  "detectedLevel": decimal number of target level,
  "detectedTone": "main detected tone",
  "detectedTechnique": "detected application technique",
  "detectedTones": ["list of present tones"],
  "viabilityScore": 0-100,
  "estimatedSessions": number of sessions needed,
  "requiredProcesses": ["required processes"],
  "confidence": confidence percentage,
  "warnings": ["warnings if any"]
}`,

        optimized: `Analyze desired color. Current level: ${currentLevel}. JSON:
{
  "detectedLevel": target level,
  "detectedTone": "main tone",
  "detectedTechnique": "technique",
  "detectedTones": ["tones"],
  "viabilityScore": 0-100,
  "estimatedSessions": number,
  "requiredProcesses": ["processes"],
  "confidence": 0-100
}`,

        minimal: `Desired color, current:${currentLevel}. JSON:
{"detectedLevel":num,"detectedTone":"tone","detectedTechnique":"technique","detectedTones":["array"],"viabilityScore":0-100,"estimatedSessions":num,"requiredProcesses":["array"],"confidence":0-100}`
      }
    }

    return prompts[lang][template]
  }

  /**
   * Get technique-specific instructions
   */
  static getTechniqueInstructions(
    technique: string, 
    config: RegionalConfig,
    template: TemplateType = 'full'
  ): string {
    const lang = config.language
    const developerTerm = config.developerTerminology
    
    // Full instructions by technique
    const fullInstructions = {
      full_color: {
        es: `- Fórmula única para cobertura completa
- Asegurar aplicación uniforme de raíces a puntas
- Considerar crecimiento natural para mantenimiento`,
        en: `- Single formula for complete coverage
- Ensure uniform application from roots to ends
- Consider natural regrowth for maintenance`
      },
      highlights: {
        es: `- Usar técnica con papel aluminio para precisión
- Crear múltiples fórmulas si es necesario (base + mechas)
- Consistencia más espesa para evitar sangrado
- Máximo ${developerTerm} 30 vol para mechas
- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,
        en: `- Use foil technique for precision
- Create multiple formulas if needed (base + highlights)
- Thicker consistency to prevent bleeding
- Maximum ${developerTerm} 30 vol for highlights
- Consider placement pattern (full head, partial, face-framing)`
      },
      balayage: {
        es: `- Técnica de pintado a mano alzada
- Transición gradual de oscuro a claro
- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)
- Consistencia cremosa para aplicación controlada
- Efecto natural, como besado por el sol
- Considerar usar decolorante en crema o arcilla`,
        en: `- Free-hand painting technique
- Gradual transition from dark to light
- Use lower ${developerTerm} volume (20 vol max recommended)
- Creamy consistency for controlled application
- Natural, sun-kissed effect
- Consider using clay or cream lightener`
      }
      // ... más técnicas
    }

    if (template === 'minimal') {
      // Ultra-compressed version
      return lang === 'es' 
        ? `${technique}: fórmula específica`
        : `${technique}: specific formula`
    }

    if (template === 'optimized') {
      // Compressed but clear
      const key = Object.keys(fullInstructions).find(k => k === technique) || 'full_color'
      const instructions = fullInstructions[key as keyof typeof fullInstructions][lang]
      return instructions.split('\n').slice(0, 3).join('; ')
    }

    // Full version
    const key = Object.keys(fullInstructions).find(k => k === technique) || 'full_color'
    return fullInstructions[key as keyof typeof fullInstructions][lang] || ''
  }

  /**
   * Get formula generation prompt
   */
  static getFormulaPrompt(
    config: FormulaConfig,
    template: TemplateType = 'full'
  ): string {
    const lang = config.regionalConfig.language
    const technique = config.selectedTechnique
    const techniqueInstructions = this.getTechniqueInstructions(
      technique, 
      config.regionalConfig, 
      template
    )

    if (template === 'minimal') {
      return lang === 'es'
        ? `Colorista experto. ${technique}. Crear fórmula profesional con ${config.brand} ${config.line}. Diagnóstico: nivel ${config.diagnosis?.averageDepthLevel}. Resultado deseado: nivel ${config.desiredResult?.level}. Devuelve markdown con fórmula exacta, tiempos, aplicación.`
        : `Expert colorist. ${technique}. Create professional formula with ${config.brand} ${config.line}. Diagnosis: level ${config.diagnosis?.averageDepthLevel}. Desired: level ${config.desiredResult?.level}. Return markdown with exact formula, timing, application.`
    }

    if (template === 'optimized') {
      return lang === 'es'
        ? `Eres un maestro colorista creando una fórmula para ${technique}.
Marca: ${config.brand} ${config.line}
Diagnóstico: ${JSON.stringify(config.diagnosis).slice(0, 200)}...
Resultado deseado: ${JSON.stringify(config.desiredResult).slice(0, 200)}...

${techniqueInstructions}

Genera fórmula detallada con:
- Productos específicos y proporciones
- Tiempos por zona
- Técnica de aplicación
- Cuidados post-servicio

Formato: Markdown profesional.`
        : `You are a master colorist creating a formula for ${technique}.
Brand: ${config.brand} ${config.line}
Diagnosis: ${JSON.stringify(config.diagnosis).slice(0, 200)}...
Desired result: ${JSON.stringify(config.desiredResult).slice(0, 200)}...

${techniqueInstructions}

Generate detailed formula with:
- Specific products and ratios
- Timing by zone
- Application technique
- Post-service care

Format: Professional markdown.`
    }

    // Return full prompt (existing implementation)
    return this.getFullFormulaPrompt(config, lang)
  }

  /**
   * Get full formula prompt (existing implementation)
   */
  private static getFullFormulaPrompt(config: FormulaConfig, lang: 'es' | 'en'): string {
    const rc = config.regionalConfig
    const technique = config.selectedTechnique
    const techniqueInstructions = this.getTechniqueInstructions(technique, rc, 'full')
    
    // Get technique name
    const techniqueNames = {
      full_color: lang === 'en' ? 'Full Color' : 'Tinte Completo',
      highlights: lang === 'en' ? 'Highlights' : 'Mechas',
      balayage: 'Balayage',
      ombre: 'Ombré',
      babylights: 'Babylights',
      color_correction: lang === 'en' ? 'Color Correction' : 'Corrección de Color',
      foilyage: 'Foilyage',
      money_piece: 'Money Piece',
      chunky_highlights: lang === 'en' ? 'Chunky Highlights' : 'Mechas Gruesas',
      reverse_balayage: 'Reverse Balayage'
    }
    
    const techniqueName = config.selectedTechnique === 'custom' && config.customTechnique 
      ? config.customTechnique 
      : techniqueNames[technique as keyof typeof techniqueNames] || techniqueNames.full_color

    // Format examples
    const formatExamples = rc.measurementSystem === 'metric' 
      ? lang === 'en'
        ? `- Quantities: "40${rc.volumeUnit} of ${rc.colorTerminology} 7.1", "60${rc.volumeUnit} of ${rc.developerTerminology} 20 vol"\n  - Ratios: "1:1${rc.decimalSeparator}5" or "1:2"\n  - Weights: "15${rc.weightUnit} of lightening powder"`
        : `- Cantidades: "40${rc.volumeUnit} de ${rc.colorTerminology} 7.1", "60${rc.volumeUnit} de ${rc.developerTerminology} 20 vol"\n  - Proporciones: "1:1${rc.decimalSeparator}5" o "1:2"\n  - Pesos: "15${rc.weightUnit} de polvo decolorante"`
      : `- Quantities: "1.35${rc.volumeUnit} of ${rc.colorTerminology} 7.1", "2${rc.volumeUnit} of ${rc.developerTerminology} 20 vol"\n  - Ratios: "1:1.5" or "1:2"\n  - Weights: "0.5${rc.weightUnit} of lightening powder"`
    
    // Volume restriction
    const volumeRestriction = rc.maxDeveloperVolume < 40 
      ? lang === 'en'
        ? `IMPORTANT: In this region, the maximum allowed ${rc.developerTerminology} volume is ${rc.maxDeveloperVolume} volumes.`
        : `IMPORTANTE: En esta región, el volumen máximo permitido de ${rc.developerTerminology} es ${rc.maxDeveloperVolume} volúmenes.`
      : ''

    if (lang === 'en') {
      return `You are a master colorist creating a professional formula for a ${techniqueName} service.

REGIONAL CONFIGURATION:
- Measurement system: ${rc.measurementSystem}
- Volume unit: ${rc.volumeUnit}
- Weight unit: ${rc.weightUnit}
- Term for developer/oxidant: ${rc.developerTerminology}
- Term for color: ${rc.colorTerminology}
- Decimal separator: ${rc.decimalSeparator}
${volumeRestriction}

QUANTITY FORMAT:
${formatExamples}

TECHNIQUE-SPECIFIC REQUIREMENTS:
${techniqueInstructions}

Current diagnosis: ${JSON.stringify(config.diagnosis)}
Desired result: ${JSON.stringify(config.desiredResult)}
Brand: ${config.brand}
Line: ${config.line}
Client history: ${config.clientHistory || 'First time'}

Generate a detailed and professional formula including:
- Preparation steps specific to ${techniqueName}
- Specific formula(s) with exact proportions using ${rc.volumeUnit} and ${rc.weightUnit} units
- For techniques requiring multiple formulas (highlights, ombre, etc.), provide all necessary formulas
- Use the term "${rc.developerTerminology}" for developer/oxidant
- Use the term "${rc.colorTerminology}" for hair color
- Processing times by zone considering the specific technique
- Detailed application technique for ${techniqueName}
- Post-service care specific to this technique
- If including estimated costs, use the ${rc.currencySymbol} symbol

IMPORTANT: 
- Use EXACTLY the units and terminology specified above
- Follow the technique-specific requirements carefully
- Adapt formulation consistency and volumes based on the technique

Format: Professional markdown with clear sections.`
    } else {
      return `Eres un maestro colorista creando una fórmula profesional para un servicio de ${techniqueName}.

CONFIGURACIÓN REGIONAL:
- Sistema de medidas: ${rc.measurementSystem}
- Unidad de volumen: ${rc.volumeUnit}
- Unidad de peso: ${rc.weightUnit}
- Término para oxidante/revelador: ${rc.developerTerminology}
- Término para coloración: ${rc.colorTerminology}
- Separador decimal: ${rc.decimalSeparator}
${volumeRestriction}

FORMATO DE CANTIDADES:
${formatExamples}

REQUISITOS ESPECÍFICOS DE LA TÉCNICA:
${techniqueInstructions}

Diagnóstico actual: ${JSON.stringify(config.diagnosis)}
Resultado deseado: ${JSON.stringify(config.desiredResult)}
Marca: ${config.brand}
Línea: ${config.line}
Historial del cliente: ${config.clientHistory || 'Primera vez'}

Genera una fórmula detallada y profesional que incluya:
- Pasos de preparación específicos para ${techniqueName}
- Fórmula(s) específica(s) con proporciones exactas usando las unidades ${rc.volumeUnit} y ${rc.weightUnit}
- Para técnicas que requieren múltiples fórmulas (mechas, ombré, etc.), proporcionar todas las fórmulas necesarias
- Usa el término "${rc.developerTerminology}" para el oxidante/revelador
- Usa el término "${rc.colorTerminology}" para la coloración
- Tiempos de procesamiento por zona considerando la técnica específica
- Técnica de aplicación detallada para ${techniqueName}
- Cuidados post-servicio específicos para esta técnica
- Si incluyes costos estimados, usa el símbolo ${rc.currencySymbol}

IMPORTANTE: 
- Usa EXACTAMENTE las unidades y terminología especificadas arriba
- Sigue cuidadosamente los requisitos específicos de la técnica
- Adapta la consistencia y volúmenes de la formulación según la técnica

Formato: Markdown profesional con secciones claras.`
    }
  }

  /**
   * Calculate token savings estimate
   */
  static estimateTokenSavings(fullTemplate: string, optimizedTemplate: string): number {
    // Rough estimate: 1 token ≈ 4 characters
    const fullTokens = Math.ceil(fullTemplate.length / 4)
    const optimizedTokens = Math.ceil(optimizedTemplate.length / 4)
    return ((fullTokens - optimizedTokens) / fullTokens) * 100
  }
}