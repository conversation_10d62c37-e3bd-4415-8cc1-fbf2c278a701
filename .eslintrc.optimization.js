/**
 * ESLint Configuration - Optimized for Salonier
 * 
 * Configuración optimizada para detectar problemas de rendimiento
 * y aplicar mejores prácticas de React Native + TypeScript
 */

module.exports = {
  extends: [
    'expo',
    '@react-native-community',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended'
  ],
  plugins: [
    'react-hooks',
    'react-native',
    '@typescript-eslint',
    'react-perf'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  rules: {
    // ========== OPTIMIZACIÓN DE RENDIMIENTO ==========
    
    // Detectar componentes que deberían usar React.memo
    'react-perf/jsx-no-new-object-as-prop': 'warn',
    'react-perf/jsx-no-new-array-as-prop': 'warn',
    'react-perf/jsx-no-new-function-as-prop': 'warn',
    
    // Hooks de React optimizados
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // Prevenir re-renders innecesarios
    'react/jsx-no-bind': ['warn', {
      'allowArrowFunctions': true,
      'allowBind': false,
      'ignoreRefs': true
    }],
    
    // ========== TYPESCRIPT ESTRICTO ==========
    
    '@typescript-eslint/no-unused-vars': ['error', { 
      'argsIgnorePattern': '^_',
      'varsIgnorePattern': '^_'
    }],
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    
    // Preferir interfaces sobre types para mejor rendimiento
    '@typescript-eslint/consistent-type-definitions': ['warn', 'interface'],
    
    // ========== REACT NATIVE ESPECÍFICO ==========
    
    'react-native/no-unused-styles': 'warn',
    'react-native/split-platform-components': 'warn',
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'warn',
    'react-native/no-raw-text': 'off', // Permitido en nuestro caso
    
    // ========== MEJORES PRÁCTICAS GENERALES ==========
    
    // Imports organizados
    'import/order': ['warn', {
      'groups': [
        'builtin',
        'external',
        'internal',
        'parent',
        'sibling',
        'index'
      ],
      'newlines-between': 'never',
      'alphabetize': {
        'order': 'asc',
        'caseInsensitive': true
      }
    }],
    
    // Prevenir console.log en producción
    'no-console': ['warn', { 
      'allow': ['warn', 'error', 'info', 'time', 'timeEnd'] 
    }],
    
    // Detectar código muerto
    'no-unreachable': 'error',
    'no-unused-expressions': 'warn',
    
    // Preferir const sobre let cuando sea posible
    'prefer-const': 'warn',
    
    // Detectar promesas no manejadas
    'no-floating-promises': 'off', // Manejado por TypeScript
    
    // ========== REACT ESPECÍFICO ==========
    
    'react/prop-types': 'off', // Usamos TypeScript
    'react/react-in-jsx-scope': 'off', // React 17+
    'react/display-name': 'warn',
    'react/jsx-key': 'error',
    'react/no-array-index-key': 'warn',
    
    // Detectar componentes que podrían beneficiarse de memo
    'react/jsx-no-constructed-context-values': 'warn',
    
    // ========== ZUSTAND ESPECÍFICO ==========
    
    // Detectar mutaciones directas del estado
    'no-param-reassign': ['error', { 
      'props': true,
      'ignorePropertyModificationsFor': ['state', 'draft']
    }],
    
    // ========== SUPABASE/ASYNC ESPECÍFICO ==========
    
    // Asegurar manejo de errores async
    'require-await': 'warn',
    'no-return-await': 'warn',
    
    // ========== ACCESIBILIDAD ==========
    
    'react-native/no-single-element-style-arrays': 'warn',
  },
  
  // ========== CONFIGURACIÓN POR ARCHIVOS ==========
  
  overrides: [
    // Stores - Reglas específicas para Zustand
    {
      files: ['stores/**/*.ts', 'stores/**/*.tsx'],
      rules: {
        // Permitir funciones que modifican estado en stores
        'no-param-reassign': 'off',
        // Requerir tipos explícitos en stores
        '@typescript-eslint/explicit-function-return-type': 'warn'
      }
    },
    
    // Componentes - Reglas de rendimiento estrictas
    {
      files: ['components/**/*.tsx'],
      rules: {
        // Requerir displayName en componentes memoizados
        'react/display-name': 'error',
        // Detectar props que cambian frecuentemente
        'react-perf/jsx-no-new-object-as-prop': 'error',
        'react-perf/jsx-no-new-array-as-prop': 'error'
      }
    },
    
    // Servicios - Reglas para lógica de negocio
    {
      files: ['services/**/*.ts'],
      rules: {
        // Requerir manejo de errores
        'require-await': 'error',
        // Preferir tipos explícitos
        '@typescript-eslint/no-explicit-any': 'error'
      }
    },
    
    // Utils - Reglas para funciones puras
    {
      files: ['utils/**/*.ts'],
      rules: {
        // Preferir funciones puras
        'no-side-effects': 'off', // Custom rule si existe
        // Requerir JSDoc para funciones públicas
        'valid-jsdoc': 'warn'
      }
    },
    
    // Tests - Reglas relajadas
    {
      files: ['**/__tests__/**/*.ts', '**/__tests__/**/*.tsx', '**/*.test.ts', '**/*.test.tsx'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off',
        'react-perf/jsx-no-new-object-as-prop': 'off'
      }
    }
  ],
  
  // ========== CONFIGURACIÓN DE ENTORNO ==========
  
  env: {
    'react-native/react-native': true,
    'jest': true,
    'es6': true
  },
  
  settings: {
    react: {
      version: 'detect'
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json'
      }
    }
  },
  
  // ========== IGNORAR ARCHIVOS ==========
  
  ignorePatterns: [
    'node_modules/',
    '.expo/',
    'dist/',
    'build/',
    '*.generated.ts',
    'metro.config.js',
    'babel.config.js'
  ]
};
