export interface Product {
  id: string;
  name: string;
  brand: string;
  category: 'tinte' | 'oxidante' | 'decolorante' | 'matizador' | 'tratamiento' | 'aditivo' | 'pre-pigmentacion' | 'otro';
  currentStock: number;
  minStock: number;
  maxStock?: number;
  unitType: 'ml' | 'g' | 'unidad';
  unitSize: number; // Tamaño del envase (ej: 100ml, 50g)
  purchasePrice: number; // Precio de compra del envase completo
  costPerUnit: number; // Precio por ml o g calculado
  lastUpdated: string;
  isActive: boolean;
  barcode?: string;
  notes?: string;
  supplier?: string;
  lastPurchaseDate?: string;
  colorCode?: string; // Para tintes: código como "7.1", "8/43"
  line?: string; // Línea de producto
}

export interface StockMovement {
  id: string;
  productId: string;
  type: 'entrada' | 'salida' | 'ajuste' | 'consumo';
  quantity: number; // Positivo para entradas, negativo para salidas
  previousStock: number;
  newStock: number;
  reason: string;
  referenceId?: string; // ID de consulta si es consumo
  clientName?: string; // Para trazabilidad en consumos
  date: string;
  createdBy: string;
  cost?: number; // Costo del movimiento
}

export interface PricingConfiguration {
  defaultMarkupPercentage: number; // 0-500%
  roundingPolicy: 'none' | 'nearest' | 'up' | 'down';
  roundingIncrement: number; // 0.5, 1, 5, 10
  minimumServicePrice: number;
  includeTaxInPrice: boolean;
  taxPercentage: number;
  currency: string;
  currencySymbol: string;
  lastUpdated: string;
}

export interface ProductPricing {
  productId: string;
  productName: string;
  brand: string;
  unitPrice: number; // Precio por ml/g
  unitType: 'ml' | 'g';
  lastUpdated: string;
}

export interface InventoryAlert {
  id: string;
  productId: string;
  type: 'low_stock' | 'out_of_stock' | 'expiring' | 'overstock';
  severity: 'low' | 'medium' | 'high';
  message: string;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  createdAt: string;
}

export interface ConsumptionAnalysis {
  productId: string;
  productName: string;
  totalConsumed: number;
  totalCost: number;
  averagePerService: number;
  servicesCount: number;
  period: string; // 'daily' | 'weekly' | 'monthly'
  topClients: Array<{
    clientName: string;
    consumptionAmount: number;
    serviceCount: number;
  }>;
}

export interface InventoryReport {
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
  overstockCount: number;
  mostUsedProducts: Array<{
    product: Product;
    usageCount: number;
    totalConsumed: number;
  }>;
  leastUsedProducts: Array<{
    product: Product;
    daysSinceLastUse: number;
  }>;
  costByCategory: Array<{
    category: string;
    totalCost: number;
    percentage: number;
  }>;
  generatedAt: string;
}

export interface FormulationConsumption {
  formulationId: string;
  items: Array<{
    productId: string;
    productName: string;
    amount: number;
    unit: 'ml' | 'g';
    unitCost: number;
    totalCost: number;
    availableStock: number;
    isStockSufficient: boolean;
  }>;
  totalCost: number;
  hasInsufficientStock: boolean;
  insufficientProducts: string[];
}

export interface SalonConfiguration {
  businessName: string;
  inventoryControlLevel: 'solo-formulas' | 'smart-cost' | 'control-total';
  pricing: PricingConfiguration;
  notifications: {
    lowStockAlerts: boolean;
    expirationAlerts: boolean;
    restockReminders: boolean;
  };
  autoConsumption: boolean;
  requireStockValidation: boolean;
  // Regional configuration
  countryCode?: string;
  measurementSystem?: 'metric' | 'imperial';
  language?: string;
  // Lifestyle preferences
  showBudgetOptions?: boolean;
}