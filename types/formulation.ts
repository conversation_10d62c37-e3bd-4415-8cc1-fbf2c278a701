export interface ViabilityAnalysis {
  score: 'safe' | 'caution' | 'risky';
  factors: {
    levelDifference: number;
    hairHealth: 'good' | 'fair' | 'poor';
    chemicalHistory: string[];
    estimatedSessions: number;
  };
  warnings: string[];
  recommendations: string[];
}

export interface FormulaCost {
  items: {
    product: string;
    amount: string;
    unitCost: number;
    totalCost: number;
  }[];
  totalMaterialCost: number;
  suggestedServicePrice: number;
  profitMargin: number;
}

export interface BrandConversion {
  fromBrand: string;
  toBrand: string;
  conversions: {
    originalShade: string;
    equivalentShade: string;
    accuracy: 'exact' | 'close' | 'approximate';
  }[];
}

export interface PreviousFormula {
  id: string;
  date: string;
  formula: string;
  brand: string;
  line: string;
  result: 'excelente' | 'bueno' | 'regular' | 'malo';
  satisfaction: number;
  notes: string;
  processingTime: number;
  oxidantVolume: string;
}

export interface ColorFormula {
  id?: string;
  brand: string;
  line: string;
  colors: {
    tone: string;  // Changed from shade to tone for consistency
    amount: number; // Changed to number
  }[];
  developerVolume: number;
  developerRatio: string;
  additives: string[];
  processingTime: number;
  technique?: string;
  formulaText?: string;
}